<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Organization;
use App\Models\Branch;
use App\Models\Setting;
use App\Models\Role;
use App\Services\ReferralTrackingService;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules;
use Illuminate\View\View;

class RegisterController extends Controller
{
    protected $referralTrackingService;

    public function __construct(ReferralTrackingService $referralTrackingService)
    {
        $this->referralTrackingService = $referralTrackingService;
    }

    /**
     * Display the registration view.
     */
    public function showRegistrationForm(Request $request): View
    {
        // Track referral if present
        $referralData = $this->referralTrackingService->trackReferral($request);

        return view('auth.register', compact('referralData'));
    }

    /**
     * Handle an incoming registration request.
     */
    public function register(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:'.User::class],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'organization_name' => ['required', 'string', 'max:255'],
            'branch_name' => ['required', 'string', 'max:255'],
            'branch_email' => ['nullable', 'string', 'email', 'max:255'],
            'branch_phone' => ['nullable', 'string', 'max:20'],
            'branch_address' => ['nullable', 'string', 'max:255'],
            'branch_description' => ['nullable', 'string'],
        ]);

        try {
            DB::beginTransaction();

            // Create organization
            $organization = Organization::create([
                'name' => $request->organization_name,
                'email' => $request->email,
            ]);

            // Create user with organization owner role
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'organization_id' => $organization->id,
                'status' => User::STATUS_ACTIVE,
            ]);

            // Assign organization owner role
            $ownerRole = Role::where('name', 'Organization Owner')->first();
            if ($ownerRole) {
                $user->roles()->attach($ownerRole);
            }

            // Create branch
            $branch = Branch::create([
                'name' => $request->branch_name,
                'email' => $request->branch_email,
                'phone' => $request->branch_phone,
                'address' => $request->branch_address,
                'description' => $request->branch_description,
                'organization_id' => $organization->id,
            ]);

            // Assign user to branch
            $user->update(['branch_id' => $branch->id]);

            // Create or update settings for the receipt
            // Create organization-specific settings
            $setting = new Setting([
                'organization_id' => $organization->id,
                'app_name' => 'Order Flow Pro',
                'app_slogan' => 'Manage Orders & Reports',
                'theme_mode' => 'light',
                'primary_color' => '#1f2937',
                'sidebar_color' => '#1f2937',
                'site_title' => 'OFP',
                'organization_name' => $request->organization_name,
                'company_address' => $request->branch_address,
                'company_phone' => $request->branch_phone,
                'company_email' => $request->branch_email
            ]);
            $setting->save();

            // Handle referral tracking if present
            $referralData = $this->referralTrackingService->getStoredReferral($request);
            if ($referralData) {
                $this->referralTrackingService->createReferral($organization, $referralData);
            }

            DB::commit();

            event(new Registered($user));

            Auth::login($user);

            return redirect()->route('plan-change.index')
                ->with('success', 'Registration successful! Please select a subscription plan to get started.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Registration failed: ' . $e->getMessage()])->withInput();
        }
    }
}
