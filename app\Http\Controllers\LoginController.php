<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use App\Providers\RouteServiceProvider;
use App\Models\User;

class LoginController extends Controller
{
    public function __construct()
    {
        $this->middleware('web');
        $this->middleware('guest')->except('logout');
    }

    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => ['required', 'string'],
            'password' => ['required', 'string'],
        ]);

        // Check if user exists and is active
        $user = User::where('email', $credentials['email'])->first();
        if (!$user) {
            throw ValidationException::withMessages([
                'email' => ['Invalid username or password.'],
            ]);
        }

        // Check if this user has an affiliate account - prevent organization login
        $affiliate = \App\Models\Affiliate::where('user_id', $user->id)->first();
        if ($affiliate) {
            throw ValidationException::withMessages([
                'email' => ['This email is registered as an affiliate. Please use the affiliate login page.'],
            ]);
        }

        // Check if user has organization access (must have organization_id)
        if (!$user->organization_id) {
            throw ValidationException::withMessages([
                'email' => ['This account is not associated with any organization. Please contact support.'],
            ]);
        }

        if ($user->status === 'inactive') {
            throw ValidationException::withMessages([
                'email' => ['Your account is inactive. Please contact the administrator.'],
            ]);
        }

        if (Auth::attempt($credentials, $request->boolean('remember'))) {
            $request->session()->regenerate();

            return redirect()->intended(route('dashboard'));
        }

        throw ValidationException::withMessages([
            'email' => ['Invalid username or password.'],
        ]);
    }

    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect('/');
    }
}
