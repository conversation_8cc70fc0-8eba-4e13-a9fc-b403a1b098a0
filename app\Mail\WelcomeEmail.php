<?php

namespace App\Mail;

use App\Models\User;
use App\Models\WelcomeMessage;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class WelcomeEmail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $user;
    public $welcomeMessage;
    public $userType;

    /**
     * Create a new message instance.
     */
    public function __construct(User $user, string $userType = 'organization')
    {
        $this->user = $user;
        $this->userType = $userType;
        $this->welcomeMessage = WelcomeMessage::getActiveMessage($userType);
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subject = $this->welcomeMessage 
            ? $this->welcomeMessage->subject 
            : 'Welcome to Sales Management System!';

        return new Envelope(
            subject: $subject,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.welcome',
            with: [
                'user' => $this->user,
                'welcomeMessage' => $this->welcomeMessage,
                'userType' => $this->userType,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
