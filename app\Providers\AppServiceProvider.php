<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Gate;
use App\Models\User;
use App\Models\Role;
use App\Models\Setting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Date;
use App\Http\Middleware\RoleMiddleware;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Models\SubscriptionPayment;
use App\Observers\SubscriptionPaymentObserver;
use App\View\Composers\SubscriptionStatusComposer;
use Illuminate\Support\Facades\View;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Bind RoleMiddleware into the service container
        $this->app->bind('role', function ($app) {
            return new RoleMiddleware();
        });

        // Bind OrganizationMiddleware into the service container
        $this->app->bind('organization', function ($app) {
            return new \App\Http\Middleware\EnsureUserBelongsToOrganization();
        });

        // Bind ProrationService into the service container
        $this->app->bind(\App\Services\ProrationService::class, function ($app) {
            return new \App\Services\ProrationService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Set application timezone from settings
        try {
            // Clear existing timezone cache to ensure fresh settings
            Cache::forget('app_timezone');

            $timezone = Cache::remember('app_timezone', 60 * 60, function () {
                // Try to get organization-specific setting first if user is logged in
                if (Auth::check()) {
                    $setting = Setting::where('organization_id', Auth::user()->organization_id)->first();
                    if ($setting && !empty($setting->timezone)) {
                        Log::info("Loading timezone from organization settings: {$setting->timezone}");
                        return $setting->timezone;
                    }
                }

                // Fall back to the first setting or UTC
                $setting = Setting::first();
                $timezone = $setting && !empty($setting->timezone) ? $setting->timezone : 'UTC';
                Log::info("Loading default timezone: {$timezone}");
                return $timezone;
            });

            // Apply timezone setting
            date_default_timezone_set($timezone);
            Config::set('app.timezone', $timezone);

            // Log the applied timezone for debugging
            Log::info("Applied timezone: " . config('app.timezone'));
            Log::info("Current time: " . now()->format('Y-m-d H:i:s'));
            Log::info("UTC time: " . now()->setTimezone('UTC')->format('Y-m-d H:i:s'));

        } catch (\Exception $e) {
            // If there's an error, use UTC and log the error
            Log::error("Error setting timezone: " . $e->getMessage());
            Config::set('app.timezone', 'UTC');
        }

        // Define role-based gates
        Gate::before(function (User $user) {
            if ($user->hasRole('Organization Owner')) {
                return true;
            }
        });

        // Register role middleware
        $this->app['router']->aliasMiddleware('role', RoleMiddleware::class);

        // Eager load roles when loading users
        User::with('roles');

        // Register model observers
        SubscriptionPayment::observe(SubscriptionPaymentObserver::class);

        // Clear currency and timezone cache when settings are updated
        Setting::updated(function (Setting $setting) {
            if ($setting->isDirty('currency_symbol') || $setting->isDirty('currency_code')) {
                Cache::forget('currency_symbol');
                Cache::forget('currency_code');
            }

            if ($setting->isDirty('timezone')) {
                $oldTimezone = $setting->getOriginal('timezone');
                $newTimezone = $setting->timezone;

                Log::info("Timezone changed: {$oldTimezone} → {$newTimezone}");

                // Clear the timezone cache
                Cache::forget('app_timezone');

                // Apply the new timezone immediately
                try {
                    date_default_timezone_set($newTimezone);
                    Config::set('app.timezone', $newTimezone);
                    Log::info("New timezone applied: " . config('app.timezone'));
                    Log::info("Current time: " . now()->format('Y-m-d H:i:s'));
                } catch (\Exception $e) {
                    Log::error("Error applying new timezone: " . $e->getMessage());
                }
            }
        });

        // Register subscription status composer for authenticated layouts
        View::composer([
            'layouts.app',
            'layouts.dashboard',
            'dashboard'
        ], SubscriptionStatusComposer::class);
    }
}

