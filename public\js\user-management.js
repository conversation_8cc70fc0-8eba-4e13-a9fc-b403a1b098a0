// User management utilities
console.log('User Management JS loaded - Version 2.0.3');

// Make UserManagement globally accessible
window.UserManagement = {
    async toggleStatus(userId) {
        try {
            const response = await fetch(`/SalesManagementSystem/users/${userId}/toggle-status`, {
                method: 'PATCH',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Failed to update user status');
            }

            // Show success message
            Swal.fire({
                title: 'Success!',
                text: data.message,
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });

            // Refresh the page after a short delay
            setTimeout(() => window.location.reload(), 2000);

        } catch (error) {
            Swal.fire({
                title: 'Error!',
                text: error.message,
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    },

    confirmDelete(userId) {
        Swal.fire({
            title: 'Are you sure?',
            text: "This action cannot be undone!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete user',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                // Create a new form element
                const deleteForm = document.createElement('form');
                deleteForm.method = 'POST';
                deleteForm.action = `/SalesManagementSystem/test-delete-user/${userId}`; // Use the test route with full path
                deleteForm.style.display = 'none';

                // Add CSRF token
                const csrfToken = document.querySelector('meta[name="csrf-token"]').content;
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = '_token';
                csrfInput.value = csrfToken;
                deleteForm.appendChild(csrfInput);

                // Append to document and submit
                document.body.appendChild(deleteForm);
                deleteForm.submit();
            }
        });
    },

    validatePasswordMatch() {
        const password = document.getElementById('password');
        const confirmation = document.getElementById('password_confirmation');
        const submitButton = document.querySelector('button[type="submit"]');

        if (password.value || confirmation.value) {
            if (password.value !== confirmation.value) {
                confirmation.setCustomValidity("Passwords don't match");
                submitButton.disabled = true;
            } else {
                confirmation.setCustomValidity('');
                submitButton.disabled = false;
            }
        }
    },

    validateForm() {
        const form = document.querySelector('form');
        const roleCheckboxes = document.querySelectorAll('input[name="roles[]"]');
        const emailInput = document.getElementById('email');
        const currentEmail = emailInput?.placeholder || '';

        form.addEventListener('submit', (e) => {
            const hasRoles = Array.from(roleCheckboxes).some(cb => cb.checked);

            // Allow empty email since it will use the current email
            if (emailInput && emailInput.value && !emailInput.validity.valid) {
                e.preventDefault();
                Swal.fire({
                    title: 'Error!',
                    text: 'Please enter a valid email address or leave it empty to keep the current one',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return;
            }

            if (!hasRoles) {
                e.preventDefault();
                Swal.fire({
                    title: 'Error!',
                    text: 'Please select at least one role',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    },

    init() {
        // Initialize password validation on relevant pages
        const passwordInputs = document.querySelectorAll('#password, #password_confirmation');
        if (passwordInputs.length) {
            passwordInputs.forEach(input => {
                input.addEventListener('input', this.validatePasswordMatch);
            });
        }

        // Initialize form validation
        const userForm = document.querySelector('form');
        if (userForm) {
            this.validateForm();
        }

        // Initialize email validation
        const emailInput = document.getElementById('email');
        if (emailInput) {
            emailInput.addEventListener('input', (e) => {
                const errorElement = document.getElementById('email-error');
                if (e.target.value && !e.target.validity.valid) {
                    errorElement.textContent = 'Please enter a valid email address';
                    errorElement.classList.remove('hidden');
                } else {
                    errorElement.classList.add('hidden');
                }
            });
        }
    },

    animateValue: function(obj, start, end, duration) {
        if (start === end) return;

        const range = end - start;
        let current = start;
        const increment = end > start ? 1 : -1;
        const stepTime = Math.abs(Math.floor(duration / range));

        const timer = setInterval(function() {
            current += increment;
            obj.textContent = current.toLocaleString();

            if (current === end) {
                clearInterval(timer);
            }
        }, stepTime);
    },

    initializeStats: function() {
        console.log('Initializing stats animation...');
        document.querySelectorAll('.stat-value').forEach(stat => {
            const value = parseInt(stat.dataset.value) || 0;
            console.log('Animating stat:', stat, 'to value:', value);
            this.animateValue(stat, 0, value, 1000);
        });
    },

    filterUsers: function() {
        const searchTerm = document.getElementById('userSearch').value.toLowerCase();
        const selectedRole = document.getElementById('roleFilter').value;
        const selectedStatus = document.getElementById('statusFilter').value;
        const rows = document.querySelectorAll('tbody tr');
        let visibleCount = 0;

        rows.forEach(row => {
            const name = row.cells[0].textContent.toLowerCase();
            const email = row.cells[1].textContent.toLowerCase();
            const roles = Array.from(row.querySelectorAll('.role-badge')).map(badge => badge.dataset.roleId);
            const statusBadge = row.querySelector('.status-badge');
            const status = statusBadge ? statusBadge.dataset.status : 'active';

            const matchesSearch = name.includes(searchTerm) || email.includes(searchTerm);
            const matchesRole = !selectedRole || roles.includes(selectedRole);
            const matchesStatus = !selectedStatus || status === selectedStatus;

            const shouldShow = matchesSearch && matchesRole && matchesStatus;
            row.style.display = shouldShow ? '' : 'none';
            if (shouldShow) visibleCount++;
        });

        // Toggle empty state
        const emptyState = document.querySelector('.empty-state');
        if (emptyState) {
            emptyState.style.display = visibleCount === 0 ? 'block' : 'none';
        }
    }
};

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing UserManagement');
    UserManagement.init();

    // Initialize stats counters
    UserManagement.initializeStats();

    // Set up delete button event listeners
    const deleteButtons = document.querySelectorAll('.delete-user-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.dataset.userId;
            UserManagement.confirmDelete(userId);
        });
    });

    // Add event listeners for filters
    const userSearch = document.getElementById('userSearch');
    const roleFilter = document.getElementById('roleFilter');
    const statusFilter = document.getElementById('statusFilter');

    if (userSearch) userSearch.addEventListener('input', () => UserManagement.filterUsers());
    if (roleFilter) roleFilter.addEventListener('change', () => UserManagement.filterUsers());
    if (statusFilter) statusFilter.addEventListener('change', () => UserManagement.filterUsers());
});

// Form validation for user edit form
const editUserForm = document.getElementById('editUserForm');
if (editUserForm) {
    editUserForm.addEventListener('submit', function(e) {
        let isValid = true;
        const password = document.getElementById('password');
        const confirmation = document.getElementById('password_confirmation');
        const email = document.getElementById('email');

        // Reset previous error messages
        document.querySelectorAll('.text-red-600').forEach(el => el.classList.add('hidden'));

        // Validate required fields
        this.querySelectorAll('[required]').forEach(field => {
            if (!field.value) {
                isValid = false;
                const errorEl = document.getElementById(`${field.id}-error`);
                if (errorEl) {
                    errorEl.textContent = `${field.previousElementSibling.textContent.trim()} is required`;
                    errorEl.classList.remove('hidden');
                }
            }
        });

        // Validate password match if either password field is filled
        if (password.value || confirmation.value) {
            if (password.value !== confirmation.value) {
                isValid = false;
                document.getElementById('password-confirmation-error').textContent = 'Passwords do not match';
                document.getElementById('password-confirmation-error').classList.remove('hidden');
            }
        }

        // Validate role selection
        const roleCheckboxes = document.querySelectorAll('input[name="roles[]"]:checked:not([disabled])');
        if (roleCheckboxes.length === 0) {
            isValid = false;
            document.getElementById('roles-error').textContent = 'Please select at least one role';
            document.getElementById('roles-error').classList.remove('hidden');
        }

        if (!isValid) {
            e.preventDefault();
            Swal.fire({
                title: 'Validation Error',
                text: 'Please check the form for errors',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        } else {
            // Show confirmation dialog for status change if status is being changed to inactive
            const statusSelect = document.getElementById('status');
            if (statusSelect.value === 'inactive' && !statusSelect.disabled) {
                e.preventDefault();
                Swal.fire({
                    title: 'Confirm Status Change',
                    text: 'Changing status to inactive will log out the user from all devices. Continue?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Yes, change status',
                    cancelButtonText: 'No, keep active'
                }).then((result) => {
                    if (result.isConfirmed) {
                        this.submit();
                    } else {
                        statusSelect.value = 'active';
                    }
                });
            }
        }
    });
}

