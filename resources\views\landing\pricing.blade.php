@extends('landing.layouts.app')

@section('title', 'Pricing - Sales Management System')
@section('description', 'Choose the perfect plan for your business. Transparent pricing with no hidden fees. Start with our free trial and scale as you grow.')

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 mx-auto text-center hero-content">
                <h1 class="display-4 fw-bold mb-4" data-aos="fade-up">
                    Simple, Transparent
                    <span class="text-warning">Pricing</span>
                </h1>
                <p class="lead mb-4" data-aos="fade-up" data-aos-delay="100">
                    Choose the plan that fits your business needs. All plans include core features
                    with no hidden fees or long-term contracts.
                </p>

                <!-- Billing Period Toggle -->
                <div class="d-flex justify-content-center mb-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="btn-group" role="group" aria-label="Billing Period">
                        <input type="radio" class="btn-check" name="billing_period" id="monthly" value="monthly" checked>
                        <label class="btn btn-outline-light" for="monthly">Monthly</label>

                        <input type="radio" class="btn-check" name="billing_period" id="annual" value="annual">
                        <label class="btn btn-outline-light" for="annual">
                            Annual <span class="badge bg-warning ms-2">Save up to 25%</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Pricing Plans Section -->
<section class="section-padding">
    <div class="container">
        @if($plans->count() > 0)
        <div class="row g-4 justify-content-center">
            @foreach($plans as $plan)
            <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                <div class="card pricing-card {{ $plan->is_featured ? 'featured' : '' }} h-100"
                     data-plan-id="{{ $plan->id }}"
                     data-monthly-price="{{ $plan->price }}"
                     data-annual-price="{{ $plan->annual_price }}"
                     data-annual-discount="{{ $plan->annual_discount_percentage }}">

                    @if($plan->is_featured)
                    <div style="height: 40px;"></div>
                    @endif

                    <div class="card-body text-center p-4">
                        <h4 class="fw-bold mb-3">{{ $plan->name }}</h4>

                        <div class="mb-4">
                            <span class="display-4 fw-bold text-primary plan-price">${{ number_format($plan->price, 0) }}</span>
                            <span class="text-muted plan-period">/month</span>
                        </div>

                        <!-- Annual Savings Display -->
                        <div class="plan-savings mb-3" style="display: none;">
                            <!-- Populated by JavaScript -->
                        </div>

                        <p class="text-muted mb-4">{{ $plan->description }}</p>

                        <ul class="list-unstyled mb-4 text-start">
                            <li class="mb-3">
                                <i class="fas fa-check text-success me-3"></i>
                                <strong>
                                    {{ $plan->branch_limit == 999 ? 'Unlimited' : $plan->branch_limit }}
                                    {{ $plan->branch_limit == 1 ? 'Branch' : 'Branches' }}
                                </strong>
                            </li>
                            <li class="mb-3">
                                <i class="fas fa-check text-success me-3"></i>
                                <strong>{{ $plan->user_limit == 999 ? 'Unlimited' : $plan->user_limit }} Users</strong>
                            </li>
                            <li class="mb-3">
                                <i class="fas fa-check text-success me-3"></i>
                                <strong>
                                    @if($plan->order_limit)
                                        {{ number_format($plan->order_limit) }} Orders/month
                                    @else
                                        Unlimited Orders
                                    @endif
                                </strong>
                            </li>
                            <li class="mb-3">
                                <i class="fas fa-check text-success me-3"></i>
                                {{ $plan->data_retention_days == 999 ? 'Forever' : $plan->data_retention_days . ' Days' }} Data Retention
                            </li>

                            @if($plan->thermal_printing)
                            <li class="mb-3">
                                <i class="fas fa-check text-success me-3"></i>
                                Thermal Printing Support
                            </li>
                            @endif

                            @if($plan->advanced_reporting)
                            <li class="mb-3">
                                <i class="fas fa-check text-success me-3"></i>
                                Advanced Reporting & Analytics
                            </li>
                            @endif

                            @if($plan->api_access)
                            <li class="mb-3">
                                <i class="fas fa-check text-success me-3"></i>
                                API Access & Integrations
                            </li>
                            @endif

                            @if($plan->white_label)
                            <li class="mb-3">
                                <i class="fas fa-check text-success me-3"></i>
                                White Label Solution
                            </li>
                            @endif

                            @if($plan->custom_branding)
                            <li class="mb-3">
                                <i class="fas fa-check text-success me-3"></i>
                                Custom Branding
                            </li>
                            @endif

                            <li class="mb-3">
                                <i class="fas fa-check text-success me-3"></i>
                                24/7 Email Support
                            </li>

                            @if($plan->is_featured)
                            <li class="mb-3">
                                <i class="fas fa-check text-success me-3"></i>
                                Priority Support
                            </li>
                            @endif
                        </ul>

                        <a href="{{ route('register') }}" class="btn {{ $plan->is_featured ? 'btn-primary' : 'btn-outline-primary' }} w-100 btn-lg">
                            Select Plan
                        </a>

                        <small class="text-muted d-block mt-2">14-day free trial • No credit card required</small>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        @else
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <div class="alert alert-info">
                    <h4>Pricing Plans Coming Soon</h4>
                    <p class="mb-0">We're currently setting up our pricing plans. Please contact us for more information about our services.</p>
                </div>
            </div>
        </div>
        @endif
    </div>
</section>

<!-- Features Comparison Section -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5" data-aos="fade-up">
                <h2 class="display-5 fw-bold text-gradient mb-3">
                    All Plans Include
                </h2>
                <p class="lead text-muted">
                    Every plan comes with essential features to help you manage your business effectively.
                </p>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="100">
                <div class="text-center">
                    <div class="feature-icon mx-auto mb-3">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <h5 class="fw-bold mb-2">Order Management</h5>
                    <p class="text-muted">Complete order lifecycle management</p>
                </div>
            </div>

            <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="200">
                <div class="text-center">
                    <div class="feature-icon mx-auto mb-3">
                        <i class="fas fa-users"></i>
                    </div>
                    <h5 class="fw-bold mb-2">Team Management</h5>
                    <p class="text-muted">Role-based access control</p>
                </div>
            </div>

            <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="300">
                <div class="text-center">
                    <div class="feature-icon mx-auto mb-3">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h5 class="fw-bold mb-2">Basic Analytics</h5>
                    <p class="text-muted">Essential business insights</p>
                </div>
            </div>

            <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="400">
                <div class="text-center">
                    <div class="feature-icon mx-auto mb-3">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h5 class="fw-bold mb-2">Secure & Reliable</h5>
                    <p class="text-muted">Enterprise-grade security</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="text-center mb-5" data-aos="fade-up">
                    <h2 class="display-5 fw-bold text-gradient mb-3">
                        Frequently Asked Questions
                    </h2>
                </div>

                <div class="accordion" id="pricingFAQ" data-aos="fade-up">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                Can I change my plan anytime?
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#pricingFAQ">
                            <div class="accordion-body">
                                Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately, and we'll prorate any billing adjustments.
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                Is there a free trial?
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#pricingFAQ">
                            <div class="accordion-body">
                                Absolutely! We offer a 30-day free trial with full access to all features. No credit card required to start.
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                What payment methods do you accept?
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#pricingFAQ">
                            <div class="accordion-body">
                                We accept all major credit cards, PayPal, and bank transfers. All payments are processed securely through our encrypted payment system.
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                Can I cancel anytime?
                            </button>
                        </h2>
                        <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#pricingFAQ">
                            <div class="accordion-body">
                                Yes, you can cancel your subscription at any time. There are no cancellation fees or long-term contracts. Your data will be available for download for 30 days after cancellation.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section-padding bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8" data-aos="fade-right">
                <h2 class="display-5 fw-bold mb-3">
                    Ready to Get Started?
                </h2>
                <p class="lead mb-0">
                    Join thousands of businesses already using our platform.
                    Start your free trial today!
                </p>
            </div>
            <div class="col-lg-4 text-lg-end" data-aos="fade-left">
                <a href="{{ route('register') }}" class="btn btn-warning btn-lg">
                    <i class="fas fa-rocket me-2"></i>Start Free Trial
                </a>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle billing period toggle
    const monthlyRadio = document.getElementById('monthly');
    const annualRadio = document.getElementById('annual');

    function updatePricing() {
        const isAnnual = annualRadio.checked;
        const planCards = document.querySelectorAll('.pricing-card');

        planCards.forEach(card => {
            const monthlyPrice = parseFloat(card.dataset.monthlyPrice || 0);
            const annualPrice = parseFloat(card.dataset.annualPrice || 0);
            const annualDiscount = parseInt(card.dataset.annualDiscount || 0);

            const priceElement = card.querySelector('.plan-price');
            const periodElement = card.querySelector('.plan-period');
            const savingsElement = card.querySelector('.plan-savings');

            if (isAnnual) {
                let effectiveAnnualPrice = annualPrice;
                if (!effectiveAnnualPrice && annualDiscount > 0) {
                    const monthlyTotal = monthlyPrice * 12;
                    effectiveAnnualPrice = monthlyTotal * (1 - annualDiscount / 100);
                }

                if (effectiveAnnualPrice > 0) {
                    priceElement.textContent = '$' + Math.round(effectiveAnnualPrice);
                    periodElement.textContent = '/year';

                    const monthlyTotal = monthlyPrice * 12;
                    const savings = monthlyTotal - effectiveAnnualPrice;
                    const savingsPercentage = Math.round((savings / monthlyTotal) * 100);

                    if (savings > 0) {
                        savingsElement.innerHTML = `<small class="text-success fw-bold"><i class="fas fa-tag me-1"></i>Save $${Math.round(savings)} (${savingsPercentage}%) annually</small>`;
                        savingsElement.style.display = 'block';
                    }
                } else {
                    // Fallback to monthly
                    priceElement.textContent = '$' + Math.round(monthlyPrice);
                    periodElement.textContent = '/month';
                    savingsElement.style.display = 'none';
                }
            } else {
                priceElement.textContent = '$' + Math.round(monthlyPrice);
                periodElement.textContent = '/month';
                savingsElement.style.display = 'none';
            }
        });
    }

    monthlyRadio.addEventListener('change', updatePricing);
    annualRadio.addEventListener('change', updatePricing);

    // Initialize
    updatePricing();
});
</script>
@endpush
@endsection
