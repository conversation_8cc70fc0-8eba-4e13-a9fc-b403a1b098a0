@php
    if (auth()->check()) {
        $settings = \App\Models\Setting::where('organization_id', auth()->user()->organization_id)->first()
                    ?? new \App\Models\Setting(['organization_id' => auth()->user()->organization_id]);
    } else {
        $settings = new \App\Models\Setting;
    }
@endphp
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="{{ $settings->theme_mode }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $settings->site_title }} - {{ $settings->app_name }}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ $settings->favicon_url }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Bootstrap and jQuery -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    @production
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @else
    <script src="https://cdn.tailwindcss.com"></script>
    @endproduction

    <!-- Add this in the head section -->
    <script src="{{ asset('vendor/js/sweetalert2.min.js') }}"></script>

    <!-- Fallback CSS for offline scenarios -->
    <style>
        /* Critical CSS for basic layout */
        .sidebar { width: 16rem; }
        .sidebar-icon { width: 1.25rem; height: 1.25rem; }
        /* Add other critical styles here */
    </style>

    <!-- Offline detection -->
    <script>
        function handleOffline() {
            document.documentElement.classList.add('offline-mode');
        }
        function handleOnline() {
            document.documentElement.classList.remove('offline-mode');
        }
        window.addEventListener('offline', handleOffline);
        window.addEventListener('online', handleOnline);
        if (!navigator.onLine) handleOffline();
    </script>

    <style>
        :root {
            --primary-color: {{ $settings->primary_color }};
            --sidebar-color: {{ $settings->sidebar_color }};
            --primary-color-80: {{ $settings->primary_color }}cc;
            --primary-color-60: {{ $settings->primary_color }}99;
        }

        /* Apply primary color to various elements */
        .bg-primary {
            background-color: var(--primary-color) !important;
        }

        .bg-primary-80 {
            background-color: var(--primary-color-80) !important;
        }

        .bg-primary-60 {
            background-color: var(--primary-color-60) !important;
        }

        .text-primary {
            color: var(--primary-color) !important;
        }

        .border-primary {
            border-color: var(--primary-color) !important;
        }

        /* Apply to focus states */
        .focus\:ring-primary:focus {
            --tw-ring-color: var(--primary-color) !important;
        }

        .hover\:bg-primary:hover {
            background-color: var(--primary-color) !important;
        }
    </style>

    <!-- Add manifest for PWA -->
    <link rel="manifest" href="/manifest.json">

    <!-- Add fallback fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* Fallback styles for offline use */
        .sidebar-icon {
            width: 1.5rem;
            height: 1.5rem;
            min-width: 1.5rem;
        }

        /* Ensure consistent font loading */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
    </style>

    <!-- Critical CSS -->
    <style>
        /* Base styles that prevent FOUC (Flash of Unstyled Content) */
        body {
            display: none;
        }

        .sidebar-icon {
            width: 1.5rem;
            height: 1.5rem;
            min-width: 1.5rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        /* Basic layout structure */
        .min-h-screen { min-height: 100vh; }
        .flex { display: flex; }
        .flex-1 { flex: 1 1 0%; }
        .flex-shrink-0 { flex-shrink: 0; }

        /* Sidebar basic styles */
        .w-64 { width: 16rem; }
        .bg-gray-800 { background-color: #1f2937; }
        .text-white { color: #ffffff; }

        /* Navigation styles */
        .space-y-2 > * + * { margin-top: 0.5rem; }
        .p-4 { padding: 1rem; }
        .rounded { border-radius: 0.25rem; }
    </style>

    <!-- Show body once CSS is loaded -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.body.style.display = 'block';
        });
    </script>

    <!-- Download fonts asynchronously -->
    <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
    <style>
        @font-face {
            font-family: 'Inter';
            font-weight: 100 900;
            font-display: swap;
            font-style: normal;
            font-named-instance: 'Regular';
            src: url("/fonts/inter-var.woff2") format("woff2");
        }

        @font-face {
            font-family: 'Font Awesome 5 Free';
            font-style: normal;
            font-weight: 900;
            font-display: swap;
            src: url("/webfonts/fa-solid-900.woff2") format("woff2");
        }
    </style>

    <!-- Styles -->
    <link href="{{ asset('vendor/css/tailwind.min.css') }}" rel="stylesheet">
    <link href="{{ asset('vendor/css/fontawesome.min.css') }}" rel="stylesheet">
    <link href="{{ asset('vendor/css/sweetalert2.min.css') }}" rel="stylesheet">

    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Font Awesome -->
    <link href="{{ asset('vendor/css/fontawesome.min.css') }}" rel="stylesheet">
    <style>
        /* Fix for Font Awesome icons */
        @font-face {
            font-family: "Font Awesome 5 Free";
            font-style: normal;
            font-weight: 900;
            font-display: block;
            src: url("{{ asset('vendor/webfonts/fa-solid-900.woff2') }}") format("woff2");
        }

        @font-face {
            font-family: "Font Awesome 5 Free";
            font-style: normal;
            font-weight: 400;
            font-display: block;
            src: url("{{ asset('vendor/webfonts/fa-regular-400.woff2') }}") format("woff2");
        }

        @font-face {
            font-family: "Font Awesome 5 Brands";
            font-style: normal;
            font-weight: 400;
            font-display: block;
            src: url("{{ asset('vendor/webfonts/fa-brands-400.woff2') }}") format("woff2");
        }

        /* Make buttons more compact */
        .btn-sm {
            padding: 0.25rem 0.5rem;
        }

        /* Add gap between flex items */
        .gap-1 {
            gap: 0.25rem;
        }
    </style>

    <!-- International Telephone Input -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/css/intlTelInput.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/intlTelInput.min.js"></script>
</head>
<body class="font-sans antialiased bg-gray-100">
    <!-- Impersonation Banner -->
    @include('components.impersonation-banner')

    <div x-data="{ sidebarOpen: false }" class="relative min-h-screen md:flex">
        <!-- Mobile menu button -->
        <div class="bg-gray-800 text-gray-100 flex justify-between md:hidden fixed w-full top-0 z-40">
            <a href="{{ route('dashboard') }}" class="block p-4 text-white font-bold">{{ $settings->app_name }}</a>
            <button @click="sidebarOpen = !sidebarOpen" class="mobile-menu-button p-4 focus:outline-none focus:bg-gray-700">
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
            </button>
        </div>

        <!-- Sidebar -->
        <div x-cloak :class="{'translate-x-0': sidebarOpen, '-translate-x-full': !sidebarOpen}"
             class="sidebar bg-gray-800 text-blue-100 w-64 fixed inset-y-0 left-0 transform md:translate-x-0 transition duration-200 ease-in-out z-30 overflow-y-auto">
            <x-sidebar :settings="$settings" />
        </div>

        <!-- Content -->
        <div class="flex-1 md:ml-64">
            <div class="md:hidden h-16"><!-- Spacer for mobile header --></div>
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 min-h-screen">
                <!-- Page Heading -->
                @if (isset($header))
                    <header class="bg-white shadow">
                        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                            {{ $header }}
                        </div>
                    </header>
                @endif

                <!-- Main Content -->
                <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <!-- Announcements -->
                    <div id="announcements-container"></div>

                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- User Management JavaScript -->
    @if(request()->routeIs('users.*'))
        <script src="{{ asset('js/user-management.js') }}"></script>
    @endif

    @stack('scripts')

    <script>
    // Register Service Worker
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('ServiceWorker registered: ', registration);
                })
                .catch(error => {
                    console.log('ServiceWorker registration failed: ', error);
                });
        });
    }

    // Handle offline/online status
    window.addEventListener('online', function() {
        document.body.classList.remove('offline');
        Swal.fire({
            icon: 'success',
            title: 'Back Online',
            text: 'Your connection has been restored.',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000
        });
    });

    window.addEventListener('offline', function() {
        document.body.classList.add('offline');
        Swal.fire({
            icon: 'warning',
            title: 'You\'re Offline',
            text: 'Some features may be limited.',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000
        });
    });

    function closeAlert(id) {
        document.getElementById(id).style.display = 'none';
    }

    // Global error handler
    window.addEventListener('error', function(e) {
        console.error('🚨 JavaScript Error:', e.error);
        console.error('🚨 Error message:', e.message);
        console.error('🚨 Error source:', e.filename + ':' + e.lineno);
    });

    // Auto-hide alerts after 5 seconds
    document.addEventListener('DOMContentLoaded', function() {
        try {
            console.log('📱 DOM Content Loaded');

            const alerts = document.querySelectorAll('[id$="-alert"]');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 5000);
            });

            // Announcements are now loaded server-side in dashboard
            @auth
            console.log('✅ User is authenticated - announcements loaded server-side');
            @endauth
        } catch (error) {
            console.error('🚨 Error in DOMContentLoaded:', error);
        }
    });

    // Announcements functionality
    @auth
    function loadAnnouncements() {
        try {
            console.log('Loading announcements...');
            console.log('Current URL:', window.location.href);
            console.log('Fetching from:', '/announcements-api');

            fetch('/announcements-api')
            .then(response => {
                console.log('✅ Announcements API response received');
                console.log('📊 Response status:', response.status);
                console.log('📊 Response ok:', response.ok);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                console.log('🔄 Converting response to JSON...');
                return response.json();
            })
            .then(data => {
                console.log('✅ Announcements JSON data received:', data);
                console.log('📊 Number of announcements:', data.announcements ? data.announcements.length : 0);
                console.log('📊 Announcements array:', data.announcements);
                displayAnnouncements(data.announcements);
            })
            .catch(error => {
                console.error('Error loading announcements:', error);
                console.error('Error details:', error.message);
            });
        } catch (error) {
            console.error('🚨 Error in loadAnnouncements function:', error);
        }
    }

    function displayAnnouncements(announcements) {
        console.log('🎨 displayAnnouncements called');
        console.log('📊 Announcements parameter:', announcements);
        console.log('📊 Announcements type:', typeof announcements);
        console.log('📊 Announcements is array:', Array.isArray(announcements));

        const container = document.getElementById('announcements-container');
        console.log('📦 Container element:', container);
        console.log('📦 Container found:', !!container);

        if (!container) {
            console.error('❌ Announcements container not found!');
            return;
        }

        if (!announcements || announcements.length === 0) {
            console.log('ℹ️ No announcements to display');
            container.innerHTML = '<div class="alert alert-info"><i class="fas fa-info-circle me-2"></i>No announcements at this time.</div>';
            return;
        }

        console.log('🧹 Clearing container...');
        container.innerHTML = '';
        console.log('🔄 Displaying', announcements.length, 'announcements');

        announcements.forEach((announcement, index) => {
            console.log(`🔄 Processing announcement ${index + 1}:`, announcement);
            console.log(`📝 Title: ${announcement.title}`);
            const announcementHtml = createAnnouncementHtml(announcement);
            console.log(`📝 Generated HTML length: ${announcementHtml.length}`);
            container.insertAdjacentHTML('beforeend', announcementHtml);
            console.log(`✅ Announcement ${index + 1} added to container`);
        });

        console.log('🎉 All announcements displayed successfully!');
        console.log('📦 Final container HTML length:', container.innerHTML.length);
    }

    function createAnnouncementHtml(announcement) {
        const dismissButton = announcement.is_dismissible
            ? `<button type="button" class="btn-close" onclick="dismissAnnouncement(${announcement.id})" aria-label="Close"></button>`
            : '';

        const priorityClass = announcement.priority === 'urgent' ? 'border-danger border-3' : '';

        return `
            <div id="announcement-${announcement.id}" class="alert ${announcement.alert_class} ${priorityClass} d-flex align-items-start mb-3" role="alert">
                <i class="${announcement.icon} fa-lg me-3 mt-1"></i>
                <div class="flex-grow-1">
                    <h5 class="alert-heading mb-2">${announcement.title}</h5>
                    <div>${announcement.content}</div>
                    ${announcement.affected_features && announcement.affected_features.length > 0 ?
                        `<div class="mt-2">
                            <strong>Affected Features:</strong>
                            ${announcement.affected_features.map(feature => `<span class="badge bg-dark me-1">${feature}</span>`).join('')}
                        </div>` : ''
                    }
                    ${announcement.starts_at || announcement.ends_at ?
                        `<div class="mt-2 small">
                            ${announcement.starts_at ? `<div><strong>Start:</strong> ${new Date(announcement.starts_at).toLocaleString()}</div>` : ''}
                            ${announcement.ends_at ? `<div><strong>End:</strong> ${new Date(announcement.ends_at).toLocaleString()}</div>` : ''}
                        </div>` : ''
                    }
                </div>
                ${dismissButton}
            </div>
        `;
    }

    // dismissAnnouncement function moved to dashboard.blade.php

    // markAnnouncementAsRead function removed - not needed for server-side implementation
    @endauth
    </script>

    <style>
        [x-cloak] { display: none !important; }

        /* Mobile responsive styles */
        @media (max-width: 768px) {
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }

            .sidebar {
                height: 100vh;
                padding-top: 4rem; /* Add padding for mobile header */
            }
        }

        /* Scrollbar styling for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 2px;
        }

        .sidebar::-webkit-scrollbar-track {
            background-color: rgba(31, 41, 55, 0.8);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background-color: rgba(156, 163, 175, 0.3);
            border-radius: 1px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background-color: rgba(156, 163, 175, 0.5);
        }

        /* Firefox scrollbar */
        .sidebar {
            scrollbar-width: thin;
            scrollbar-color: rgba(156, 163, 175, 0.3) rgba(31, 41, 55, 0.8);
        }

        /* Responsive grid adjustments */
        @media (max-width: 640px) {
            .grid {
                grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
            }
        }
    </style>
</body>
</html>

