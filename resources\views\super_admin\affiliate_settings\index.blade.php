@extends('super_admin.layouts.app')

@section('title', 'Affiliate Settings')
@section('page-title', 'Affiliate Program Settings')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Affiliate Program Settings</h1>
            <p class="text-muted">Manage system-wide affiliate program configuration</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('super_admin.affiliates.index') }}" class="btn btn-secondary">
                <i class="fas fa-users me-2"></i>View Affiliates
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Affiliates
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['total_affiliates']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Active Affiliates
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['active_affiliates']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Earnings
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${{ number_format($stats['total_earnings'], 2) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending Withdrawals
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${{ number_format($stats['pending_withdrawals'] ?? 0, 2) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Statistics Row -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                Total Referrals
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['total_referrals'] ?? 0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-link fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Converted Referrals
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['converted_referrals'] ?? 0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Pending Earnings
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${{ number_format($stats['pending_earnings'] ?? 0, 2) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-hourglass-half fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-dark shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-dark text-uppercase mb-1">
                                Current Rate Users
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['affected_affiliates_count'] ?? 0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Form -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cogs me-2"></i>
                        Affiliate Program Configuration
                    </h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('super_admin.affiliate_settings.update') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <!-- Commission Settings -->
                            <div class="col-lg-6">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Commission Settings</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group mb-3">
                                            <label for="default_commission_rate" class="form-label">
                                                <strong>Default Commission Rate (%)</strong>
                                                <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input type="number"
                                                       class="form-control @error('default_commission_rate') is-invalid @enderror"
                                                       id="default_commission_rate"
                                                       name="default_commission_rate"
                                                       value="{{ old('default_commission_rate', $settings->default_commission_rate) }}"
                                                       step="0.01"
                                                       min="0"
                                                       max="100"
                                                       required>
                                                <span class="input-group-text">%</span>
                                            </div>
                                            @error('default_commission_rate')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                This rate applies to new affiliates and can be overridden individually.
                                                <br><strong>{{ $stats['affected_affiliates_count'] ?? 0 }}</strong> existing affiliates currently use this rate.
                                            </small>
                                        </div>

                                        <div class="form-check mb-3">
                                            <input type="checkbox"
                                                   class="form-check-input"
                                                   id="apply_rate_to_existing"
                                                   name="apply_rate_to_existing"
                                                   value="1"
                                                   {{ old('apply_rate_to_existing') ? 'checked' : '' }}>
                                            <label class="form-check-label" for="apply_rate_to_existing">
                                                <strong>Apply new rate to existing affiliates</strong>
                                            </label>
                                            <small class="form-text text-muted d-block">
                                                Check this to update commission rates for all existing affiliates who currently have the old default rate.
                                            </small>
                                        </div>

                                        <div class="form-check mb-3">
                                            <input type="checkbox"
                                                   class="form-check-input"
                                                   id="recurring_commissions"
                                                   name="recurring_commissions"
                                                   value="1"
                                                   {{ old('recurring_commissions', $settings->recurring_commissions) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="recurring_commissions">
                                                Enable Recurring Commissions
                                            </label>
                                            <small class="form-text text-muted d-block">
                                                Affiliates earn commissions on subscription renewals.
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Withdrawal Settings -->
                            <div class="col-lg-6">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Withdrawal Settings</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group mb-3">
                                            <label for="minimum_withdrawal" class="form-label">
                                                <strong>Minimum Withdrawal Amount</strong>
                                                <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number"
                                                       class="form-control @error('minimum_withdrawal') is-invalid @enderror"
                                                       id="minimum_withdrawal"
                                                       name="minimum_withdrawal"
                                                       value="{{ old('minimum_withdrawal', $settings->minimum_withdrawal) }}"
                                                       step="0.01"
                                                       min="0"
                                                       required>
                                            </div>
                                            @error('minimum_withdrawal')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="withdrawal_fee_percentage" class="form-label">
                                                <strong>Withdrawal Fee (%)</strong>
                                            </label>
                                            <div class="input-group">
                                                <input type="number"
                                                       class="form-control @error('withdrawal_fee_percentage') is-invalid @enderror"
                                                       id="withdrawal_fee_percentage"
                                                       name="withdrawal_fee_percentage"
                                                       value="{{ old('withdrawal_fee_percentage', $settings->withdrawal_fee_percentage) }}"
                                                       step="0.01"
                                                       min="0"
                                                       max="100">
                                                <span class="input-group-text">%</span>
                                            </div>
                                            @error('withdrawal_fee_percentage')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="withdrawal_fee_fixed" class="form-label">
                                                <strong>Fixed Withdrawal Fee</strong>
                                            </label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number"
                                                       class="form-control @error('withdrawal_fee_fixed') is-invalid @enderror"
                                                       id="withdrawal_fee_fixed"
                                                       name="withdrawal_fee_fixed"
                                                       value="{{ old('withdrawal_fee_fixed', $settings->withdrawal_fee_fixed) }}"
                                                       step="0.01"
                                                       min="0">
                                            </div>
                                            @error('withdrawal_fee_fixed')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Methods -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Payment Methods</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            @php
                                                $availableMethods = [
                                                    'bank_transfer' => 'Bank Transfer',
                                                    'paypal' => 'PayPal',
                                                    'stripe' => 'Stripe',
                                                    'manual' => 'Manual Payment'
                                                ];
                                                $selectedMethods = old('payment_methods', $settings->payment_methods ?? []);
                                            @endphp
                                            @foreach($availableMethods as $method => $label)
                                                <div class="col-md-3 mb-2">
                                                    <div class="form-check">
                                                        <input type="checkbox"
                                                               class="form-check-input"
                                                               id="payment_method_{{ $method }}"
                                                               name="payment_methods[]"
                                                               value="{{ $method }}"
                                                               {{ in_array($method, $selectedMethods) ? 'checked' : '' }}>
                                                        <label class="form-check-label" for="payment_method_{{ $method }}">
                                                            {{ $label }}
                                                        </label>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                        @error('payment_methods')
                                            <div class="text-danger mt-2">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted">
                                            Select at least one payment method for affiliate withdrawals.
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Program Settings -->
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Program Settings</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-check mb-3">
                                            <input type="checkbox"
                                                   class="form-check-input"
                                                   id="program_active"
                                                   name="program_active"
                                                   value="1"
                                                   {{ old('program_active', $settings->program_active) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="program_active">
                                                <strong>Affiliate Program Active</strong>
                                            </label>
                                            <small class="form-text text-muted d-block">
                                                Uncheck to temporarily disable new affiliate registrations.
                                            </small>
                                        </div>

                                        <div class="form-check mb-3">
                                            <input type="checkbox"
                                                   class="form-check-input"
                                                   id="auto_approve_affiliates"
                                                   name="auto_approve_affiliates"
                                                   value="1"
                                                   {{ old('auto_approve_affiliates', $settings->auto_approve_affiliates) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="auto_approve_affiliates">
                                                Auto-approve New Affiliates
                                            </label>
                                            <small class="form-text text-muted d-block">
                                                New affiliates will be automatically approved without manual review.
                                            </small>
                                        </div>

                                        <div class="form-check mb-3">
                                            <input type="checkbox"
                                                   class="form-check-input"
                                                   id="auto_approve_earnings"
                                                   name="auto_approve_earnings"
                                                   value="1"
                                                   {{ old('auto_approve_earnings', $settings->auto_approve_earnings) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="auto_approve_earnings">
                                                Auto-approve Earnings
                                            </label>
                                            <small class="form-text text-muted d-block">
                                                Commission earnings will be automatically approved and available for withdrawal.
                                            </small>
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="cookie_duration_days" class="form-label">
                                                <strong>Cookie Duration (Days)</strong>
                                                <span class="text-danger">*</span>
                                            </label>
                                            <input type="number"
                                                   class="form-control @error('cookie_duration_days') is-invalid @enderror"
                                                   id="cookie_duration_days"
                                                   name="cookie_duration_days"
                                                   value="{{ old('cookie_duration_days', $settings->cookie_duration_days) }}"
                                                   min="1"
                                                   max="365"
                                                   required>
                                            @error('cookie_duration_days')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                How long referral tracking cookies remain valid (1-365 days).
                                            </small>
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="max_referrals_per_affiliate" class="form-label">
                                                <strong>Max Referrals per Affiliate</strong>
                                            </label>
                                            <input type="number"
                                                   class="form-control @error('max_referrals_per_affiliate') is-invalid @enderror"
                                                   id="max_referrals_per_affiliate"
                                                   name="max_referrals_per_affiliate"
                                                   value="{{ old('max_referrals_per_affiliate', $settings->max_referrals_per_affiliate) }}"
                                                   min="1"
                                                   placeholder="Leave empty for unlimited">
                                            @error('max_referrals_per_affiliate')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                Optional limit on number of referrals per affiliate. Leave empty for unlimited.
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Messages & Terms</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group mb-3">
                                            <label for="welcome_message" class="form-label">
                                                <strong>Welcome Message</strong>
                                            </label>
                                            <textarea class="form-control @error('welcome_message') is-invalid @enderror"
                                                      id="welcome_message"
                                                      name="welcome_message"
                                                      rows="4"
                                                      placeholder="Welcome message for new affiliates...">{{ old('welcome_message', $settings->welcome_message) }}</textarea>
                                            @error('welcome_message')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                This message will be shown to new affiliates after registration.
                                            </small>
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="terms_and_conditions" class="form-label">
                                                <strong>Terms and Conditions</strong>
                                            </label>
                                            <textarea class="form-control @error('terms_and_conditions') is-invalid @enderror"
                                                      id="terms_and_conditions"
                                                      name="terms_and_conditions"
                                                      rows="6"
                                                      placeholder="Affiliate program terms and conditions...">{{ old('terms_and_conditions', $settings->terms_and_conditions) }}</textarea>
                                            @error('terms_and_conditions')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                Legal terms that affiliates must agree to during registration.
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <button type="submit" class="btn btn-primary btn-lg me-3">
                                            <i class="fas fa-save me-2"></i>
                                            Update Settings
                                        </button>
                                        <a href="{{ route('super_admin.affiliates.index') }}" class="btn btn-secondary btn-lg">
                                            <i class="fas fa-times me-2"></i>
                                            Cancel
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Commission Rate Change Confirmation Modal -->
<div class="modal fade" id="commissionChangeModal" tabindex="-1" aria-labelledby="commissionChangeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="commissionChangeModalLabel">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Commission Rate Change
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p><strong>You are about to change the default commission rate.</strong></p>
                <p>This will affect:</p>
                <ul>
                    <li>All new affiliates who register after this change</li>
                    <li id="existing-affiliates-text">Existing affiliates (if you checked the option above)</li>
                </ul>
                <p class="text-muted">
                    <small>
                        <i class="fas fa-info-circle me-1"></i>
                        Individual affiliate commission rates that have been manually set will not be affected.
                    </small>
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmCommissionChange">
                    <i class="fas fa-check me-2"></i>
                    Confirm Changes
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const commissionRateInput = document.getElementById('default_commission_rate');
    const applyToExistingCheckbox = document.getElementById('apply_rate_to_existing');
    const modal = new bootstrap.Modal(document.getElementById('commissionChangeModal'));
    const confirmButton = document.getElementById('confirmCommissionChange');
    const existingAffiliatesText = document.getElementById('existing-affiliates-text');

    let originalCommissionRate = {{ $settings->default_commission_rate }};

    form.addEventListener('submit', function(e) {
        const newRate = parseFloat(commissionRateInput.value);
        const applyToExisting = applyToExistingCheckbox.checked;

        // Show confirmation modal if commission rate changed
        if (newRate !== originalCommissionRate) {
            e.preventDefault();

            // Update modal text based on checkbox
            if (applyToExisting) {
                existingAffiliatesText.innerHTML = '<strong>Existing affiliates with the current default rate ({{ $settings->default_commission_rate }}%)</strong>';
                existingAffiliatesText.classList.add('text-warning');
            } else {
                existingAffiliatesText.innerHTML = 'Existing affiliates (will NOT be affected since the option is unchecked)';
                existingAffiliatesText.classList.remove('text-warning');
            }

            modal.show();
        }
    });

    confirmButton.addEventListener('click', function() {
        modal.hide();
        form.submit();
    });

    // Update existing affiliates text when checkbox changes
    applyToExistingCheckbox.addEventListener('change', function() {
        if (this.checked) {
            existingAffiliatesText.innerHTML = '<strong>Existing affiliates with the current default rate ({{ $settings->default_commission_rate }}%)</strong>';
            existingAffiliatesText.classList.add('text-warning');
        } else {
            existingAffiliatesText.innerHTML = 'Existing affiliates (will NOT be affected since the option is unchecked)';
            existingAffiliatesText.classList.remove('text-warning');
        }
    });
});
</script>
@endsection
