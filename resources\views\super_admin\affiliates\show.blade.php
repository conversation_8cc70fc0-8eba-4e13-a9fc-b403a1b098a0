@extends('super_admin.layouts.app')

@section('title', 'Affiliate Details')
@section('page-title', 'Affiliate Details')

@section('content')
<!-- Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h2 mb-1">{{ $affiliate->user->name }}</h1>
        <p class="text-muted">Affiliate ID: {{ $affiliate->affiliate_code }}</p>
    </div>
    <div>
        <a href="{{ route('super_admin.affiliates.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            Back to Affiliates
        </a>
        <a href="{{ route('super_admin.affiliates.edit', $affiliate) }}" class="btn btn-primary">
            <i class="fas fa-edit me-2"></i>
            Edit Affiliate
        </a>
    </div>
</div>

<!-- Status Badge -->
<div class="mb-4">
    @if($affiliate->status === 'active')
        <span class="badge bg-success fs-6">
            <i class="fas fa-check-circle me-1"></i>
            Active
        </span>
    @elseif($affiliate->status === 'pending')
        <span class="badge bg-warning fs-6">
            <i class="fas fa-clock me-1"></i>
            Pending Approval
        </span>
    @elseif($affiliate->status === 'suspended')
        <span class="badge bg-danger fs-6">
            <i class="fas fa-ban me-1"></i>
            Suspended
        </span>
    @else
        <span class="badge bg-secondary fs-6">
            <i class="fas fa-times-circle me-1"></i>
            Inactive
        </span>
    @endif
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <!-- Total Referrals -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Referrals
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_referrals'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Converted Referrals -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Converted Referrals
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['converted_referrals'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-handshake fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Earnings -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total Earnings
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${{ number_format($stats['total_earnings'], 2) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Available Balance -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Available Balance
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${{ number_format($stats['available_balance'], 2) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-wallet fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Click Performance Stats -->
<div class="row mb-4">
    <!-- Total Clicks -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total Clicks
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['total_clicks'] ?? 0) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-mouse-pointer fa-2x text-gray-300"></i>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-info">
                        <i class="fas fa-star"></i>
                        {{ number_format($stats['unique_clicks'] ?? 0) }} unique
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Clicks -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Today's Clicks
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['today_clicks'] ?? 0) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-clock"></i>
                        Real-time performance
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- This Month Clicks -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            This Month
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['this_month_clicks'] ?? 0) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-chart-line"></i>
                        Monthly performance
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Click Conversion Rate -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-dark shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-dark text-uppercase mb-1">
                            Click Conversion
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['click_to_registration_rate'] ?? 0, 1) }}%</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-pie fa-2x text-gray-300"></i>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        Clicks to registrations
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Referred Organizations -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Referred Organizations</h6>
    </div>
    <div class="card-body">
        @if($affiliate->referrals->count() > 0)
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Organization</th>
                            <th>Status</th>
                            <th>Joined</th>
                            <th>Subscription</th>
                            <th>Earnings</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($affiliate->referrals as $referral)
                            @if($referral->organization)
                                <tr>
                                    <td>
                                        <strong>{{ $referral->organization->name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $referral->organization->email ?? 'No email' }}</small>
                                    </td>
                                    <td>
                                        @if($referral->organization->is_active)
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-secondary">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        {{ $referral->registration_date->format('M j, Y') }}
                                    </td>
                                    <td>
                                        @if($referral->organization->activeSubscription)
                                            {{ $referral->organization->activeSubscription->plan->name ?? 'No plan' }}
                                            <br>
                                            <small class="text-muted">${{ number_format($referral->organization->activeSubscription->amount_paid ?? 0, 2) }}/month</small>
                                        @elseif($referral->organization->plan)
                                            {{ $referral->organization->plan->name }}
                                            <br>
                                            <small class="text-muted">${{ number_format($referral->organization->plan->price ?? 0, 2) }}/month</small>
                                        @else
                                            <span class="text-muted">No subscription</span>
                                        @endif
                                    </td>
                                    <td>
                                        ${{ number_format($referral->commission_earned ?? 0, 2) }}
                                    </td>
                                    <td>
                                        <a href="{{ route('super_admin.organizations.show', $referral->organization) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            @endif
                        @endforeach
                    </tbody>
                </table>
            </div>
            @if($affiliate->referrals->count() > 10)
                <div class="text-center mt-3">
                    <a href="#" class="btn btn-sm btn-outline-primary">View All Referrals</a>
                </div>
            @endif
        @else
            <p class="text-muted">No organizations have been referred by this affiliate yet.</p>
        @endif
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <!-- Affiliate Information -->
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Affiliate Information</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-gray-800">Personal Details</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Name:</strong></td>
                                <td>{{ $affiliate->user->name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>{{ $affiliate->user->email }}</td>
                            </tr>
                            <tr>
                                <td><strong>Phone:</strong></td>
                                <td>{{ $affiliate->user->phone ?? 'Not provided' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Joined:</strong></td>
                                <td>{{ $affiliate->created_at->format('M d, Y') }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-gray-800">Affiliate Details</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Affiliate Code:</strong></td>
                                <td><code>{{ $affiliate->affiliate_code }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>Commission Rate:</strong></td>
                                <td>{{ number_format($affiliate->commission_rate, 1) }}%</td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    @if($affiliate->status === 'active')
                                        <span class="badge bg-success">Active</span>
                                    @elseif($affiliate->status === 'pending')
                                        <span class="badge bg-warning">Pending</span>
                                    @elseif($affiliate->status === 'suspended')
                                        <span class="badge bg-danger">Suspended</span>
                                    @else
                                        <span class="badge bg-secondary">Inactive</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Website:</strong></td>
                                <td>
                                    @if($affiliate->website)
                                        <a href="{{ $affiliate->website }}" target="_blank">{{ $affiliate->website }}</a>
                                    @else
                                        Not provided
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                @if($affiliate->bio)
                    <div class="mt-3">
                        <h6 class="text-gray-800">Bio</h6>
                        <p class="text-muted">{{ $affiliate->bio }}</p>
                    </div>
                @endif

                @if($affiliate->payment_details)
                    <div class="mt-3">
                        <h6 class="text-gray-800">Payment Details</h6>
                        @if($affiliate->payment_details['method'] === 'paypal')
                            <p><strong>PayPal Email:</strong> {{ $affiliate->payment_details['paypal_email'] }}</p>
                        @elseif($affiliate->payment_details['method'] === 'bank_transfer')
                            <p><strong>Bank:</strong> {{ $affiliate->payment_details['bank_name'] }}</p>
                            <p><strong>Account:</strong> {{ $affiliate->payment_details['account_number'] }}</p>
                            <p><strong>Account Name:</strong> {{ $affiliate->payment_details['account_name'] }}</p>
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
            </div>
            <div class="card-body">
                @if($affiliate->status === 'pending')
                    <form action="{{ route('super_admin.affiliates.approve', $affiliate) }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-success btn-sm mb-2 w-100">
                            <i class="fas fa-check me-2"></i>
                            Approve Affiliate
                        </button>
                    </form>
                    <form action="{{ route('super_admin.affiliates.reject', $affiliate) }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-danger btn-sm mb-2 w-100"
                                onclick="return confirm('Are you sure you want to reject this affiliate?')">
                            <i class="fas fa-times me-2"></i>
                            Reject Affiliate
                        </button>
                    </form>
                @elseif($affiliate->status === 'active')
                    <form action="{{ route('super_admin.affiliates.suspend', $affiliate) }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-warning btn-sm mb-2 w-100"
                                onclick="return confirm('Are you sure you want to suspend this affiliate?')">
                            <i class="fas fa-ban me-2"></i>
                            Suspend Affiliate
                        </button>
                    </form>
                @elseif($affiliate->status === 'suspended' || $affiliate->status === 'inactive')
                    <form action="{{ route('super_admin.affiliates.reactivate', $affiliate) }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-success btn-sm mb-2 w-100">
                            <i class="fas fa-play me-2"></i>
                            Reactivate Affiliate
                        </button>
                    </form>
                @endif

                <a href="{{ route('super_admin.affiliates.edit', $affiliate) }}" class="btn btn-primary btn-sm mb-2 w-100">
                    <i class="fas fa-edit me-2"></i>
                    Edit Details
                </a>

                <button type="button" class="btn btn-info btn-sm mb-2 w-100" data-bs-toggle="modal" data-bs-target="#bonusModal">
                    <i class="fas fa-gift me-2"></i>
                    Add Bonus
                </button>

                <button type="button" class="btn btn-secondary btn-sm mb-2 w-100" data-bs-toggle="modal" data-bs-target="#adjustmentModal">
                    <i class="fas fa-calculator me-2"></i>
                    Add Adjustment
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Click Analytics Section -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-chart-line me-2"></i>
            Click Analytics & Performance
        </h6>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="loadClickAnalytics('today')">Today</button>
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="loadClickAnalytics('week')">Week</button>
            <button type="button" class="btn btn-sm btn-outline-primary active" onclick="loadClickAnalytics('month')">Month</button>
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="loadClickAnalytics('all')">All Time</button>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <!-- Click Summary -->
            <div class="col-lg-8">
                <h6 class="text-gray-800 mb-3">Click Performance Summary</h6>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="text-primary mb-1" id="total-clicks">{{ number_format($stats['total_clicks'] ?? 0) }}</h4>
                            <small class="text-muted">Total Clicks</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="text-success mb-1" id="unique-clicks">{{ number_format($stats['unique_clicks'] ?? 0) }}</h4>
                            <small class="text-muted">Unique Visitors</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="text-info mb-1" id="conversion-rate">{{ number_format($stats['click_to_registration_rate'] ?? 0, 1) }}%</h4>
                            <small class="text-muted">Conversion Rate</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="text-warning mb-1" id="today-clicks">{{ number_format($stats['today_clicks'] ?? 0) }}</h4>
                            <small class="text-muted">Today's Clicks</small>
                        </div>
                    </div>
                </div>

                <!-- Traffic Sources -->
                <div class="mt-4">
                    <h6 class="text-gray-800 mb-3">Top Traffic Sources</h6>
                    <div id="traffic-sources-loading" class="text-center py-3">
                        <i class="fas fa-spinner fa-spin"></i> Loading traffic sources...
                    </div>
                    <div id="traffic-sources-content" style="display: none;">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Quick Actions & Links -->
            <div class="col-lg-4">
                <h6 class="text-gray-800 mb-3">Affiliate Links</h6>
                <div class="mb-3">
                    <label class="form-label small text-muted">Trackable Link (Recommended)</label>
                    <div class="input-group input-group-sm">
                        <input type="text" class="form-control" value="{{ url('/go/' . $affiliate->affiliate_code) }}" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard(this.previousElementSibling)">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label small text-muted">Standard Link</label>
                    <div class="input-group input-group-sm">
                        <input type="text" class="form-control" value="{{ url('/register?ref=' . $affiliate->affiliate_code) }}" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard(this.previousElementSibling)">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>

                <!-- Device Breakdown -->
                <div class="mt-4">
                    <h6 class="text-gray-800 mb-3">Device Breakdown</h6>
                    <div id="device-breakdown-loading" class="text-center py-3">
                        <i class="fas fa-spinner fa-spin"></i> Loading device data...
                    </div>
                    <div id="device-breakdown-content" style="display: none;">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Click Analytics -->
<script>
let currentPeriod = 'month';

function loadClickAnalytics(period) {
    currentPeriod = period;

    // Update active button
    document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    // Show loading states
    document.getElementById('traffic-sources-loading').style.display = 'block';
    document.getElementById('traffic-sources-content').style.display = 'none';
    document.getElementById('device-breakdown-loading').style.display = 'block';
    document.getElementById('device-breakdown-content').style.display = 'none';

    // Fetch analytics data
    fetch(`/super-admin/affiliates/{{ $affiliate->id }}/click-analytics?period=${period}`)
        .then(response => response.json())
        .then(data => {
            updateClickStats(data.stats);
            updateTrafficSources(data.top_sources);
            updateDeviceBreakdown(data.device_breakdown);
        })
        .catch(error => {
            console.error('Error loading click analytics:', error);
            document.getElementById('traffic-sources-loading').innerHTML = '<div class="text-danger">Error loading data</div>';
            document.getElementById('device-breakdown-loading').innerHTML = '<div class="text-danger">Error loading data</div>';
        });
}

function updateClickStats(stats) {
    document.getElementById('total-clicks').textContent = stats.total_clicks.toLocaleString();
    document.getElementById('unique-clicks').textContent = stats.unique_clicks.toLocaleString();
    document.getElementById('conversion-rate').textContent = stats.conversion_rate + '%';
}

function updateTrafficSources(sources) {
    const container = document.getElementById('traffic-sources-content');
    if (sources.length === 0) {
        container.innerHTML = '<p class="text-muted">No traffic data available for this period.</p>';
    } else {
        let html = '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>Source</th><th>Clicks</th><th>Unique</th></tr></thead><tbody>';
        sources.forEach(source => {
            html += `<tr><td>${source.source || 'Direct'}</td><td>${source.clicks}</td><td>${source.unique_clicks}</td></tr>`;
        });
        html += '</tbody></table></div>';
        container.innerHTML = html;
    }

    document.getElementById('traffic-sources-loading').style.display = 'none';
    document.getElementById('traffic-sources-content').style.display = 'block';
}

function updateDeviceBreakdown(devices) {
    const container = document.getElementById('device-breakdown-content');
    if (devices.length === 0) {
        container.innerHTML = '<p class="text-muted">No device data available.</p>';
    } else {
        let html = '';
        devices.forEach(device => {
            const percentage = devices.reduce((sum, d) => sum + d.clicks, 0) > 0
                ? Math.round((device.clicks / devices.reduce((sum, d) => sum + d.clicks, 0)) * 100)
                : 0;
            html += `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="small">${device.device_type || 'Unknown'}</span>
                    <span class="badge bg-secondary">${device.clicks} (${percentage}%)</span>
                </div>
            `;
        });
        container.innerHTML = html;
    }

    document.getElementById('device-breakdown-loading').style.display = 'none';
    document.getElementById('device-breakdown-content').style.display = 'block';
}

function copyToClipboard(input) {
    input.select();
    document.execCommand('copy');

    // Show feedback
    const button = input.nextElementSibling;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i>';
    button.classList.add('btn-success');
    button.classList.remove('btn-outline-secondary');

    setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('btn-success');
        button.classList.add('btn-outline-secondary');
    }, 2000);
}

// Load initial data
document.addEventListener('DOMContentLoaded', function() {
    loadClickAnalytics('month');
});
</script>

<!-- Bonus Modal -->
<div class="modal fade" id="bonusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('super_admin.affiliates.add-bonus', $affiliate) }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Add Bonus</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="bonus_amount" class="form-label">Bonus Amount ($)</label>
                        <input type="number" class="form-control" id="bonus_amount" name="amount" step="0.01" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label for="bonus_description" class="form-label">Description</label>
                        <textarea class="form-control" id="bonus_description" name="description" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Bonus</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Adjustment Modal -->
<div class="modal fade" id="adjustmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('super_admin.affiliates.add-adjustment', $affiliate) }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Add Adjustment</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="adjustment_type" class="form-label">Type</label>
                        <select class="form-select" id="adjustment_type" name="type" required>
                            <option value="">Select type</option>
                            <option value="bonus">Bonus</option>
                            <option value="penalty">Penalty</option>
                            <option value="correction">Correction</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="adjustment_amount" class="form-label">Amount ($)</label>
                        <input type="number" class="form-control" id="adjustment_amount" name="amount" step="0.01" required>
                        <small class="form-text text-muted">Use negative values for deductions</small>
                    </div>
                    <div class="mb-3">
                        <label for="adjustment_description" class="form-label">Description</label>
                        <textarea class="form-control" id="adjustment_description" name="description" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Adjustment</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

