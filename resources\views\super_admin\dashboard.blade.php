@extends('super_admin.layouts.app')

@section('title', 'Super Admin Dashboard')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Super Admin Dashboard</h1>
        <div class="d-flex align-items-center">
            <a href="{{ route('super_admin.site_analytics.index') }}" class="btn btn-primary btn-sm me-3">
                <i class="fas fa-chart-line me-1"></i>
                Site Analytics
            </a>
            <div class="text-muted">
                <i class="fas fa-calendar-alt"></i>
                {{ now()->format('F j, Y') }}
            </div>
        </div>
    </div>

    <!-- Stats Cards Row -->
    <div class="row mb-4">
        <!-- Total Organizations -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Organizations
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($totalOrganizations) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-building fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-success">
                            <i class="fas fa-check-circle"></i>
                            {{ $activeOrganizations }} Active
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Subscriptions -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Subscriptions
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($totalSubscriptions) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-success">
                            <i class="fas fa-check-circle"></i>
                            {{ $activeSubscriptions }} Active
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monthly Revenue -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Monthly Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${{ number_format($monthlyRevenue, 2) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-info">
                            <i class="fas fa-calendar"></i>
                            {{ now()->format('F Y') }}
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Users -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Total Users
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($totalUsers) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-warning">
                            <i class="fas fa-chart-line"></i>
                            +{{ $organizationGrowth }} this month
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Second Row Stats -->
    <div class="row mb-4">
        <!-- Total Revenue -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${{ number_format($totalRevenue, 2) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Trial Organizations -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Trial Organizations
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($trialOrganizations) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            {{ $expiredTrials }} Expired
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Orders -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Orders
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($totalOrders) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Tables Row -->
    <div class="row">
        <!-- Plan Popularity Chart -->
        <div class="col-xl-6 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Plan Popularity</h6>
                </div>
                <div class="card-body">
                    @if($planStats->count() > 0)
                        @foreach($planStats as $plan)
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span class="font-weight-bold">{{ $plan->name }}</span>
                                    <span class="text-muted">{{ $plan->subscriptions_count }} subscriptions</span>
                                </div>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-primary" role="progressbar"
                                         style="width: {{ $planStats->max('subscriptions_count') > 0 ? ($plan->subscriptions_count / $planStats->max('subscriptions_count')) * 100 : 0 }}%">
                                    </div>
                                </div>
                                <small class="text-muted">${{ number_format($plan->price, 2) }}/month</small>
                            </div>
                        @endforeach
                    @else
                        <p class="text-muted">No subscription data available.</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Recent Organizations -->
        <div class="col-xl-6 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Organizations</h6>
                    <a href="{{ route('super_admin.organizations.index') }}" class="btn btn-sm btn-primary">View All</a>
                </div>
                <div class="card-body">
                    @if($recentOrganizations->count() > 0)
                        @foreach($recentOrganizations as $org)
                            <div class="d-flex align-items-center mb-3">
                                <div class="mr-3">
                                    @if($org->logo)
                                        <img src="{{ asset('storage/logos/' . $org->logo) }}"
                                             alt="{{ $org->name }}" class="rounded-circle" width="40" height="40">
                                    @else
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center"
                                             style="width: 40px; height: 40px;">
                                            <span class="text-white font-weight-bold">{{ substr($org->name, 0, 1) }}</span>
                                        </div>
                                    @endif
                                </div>
                                <div class="flex-grow-1">
                                    <div class="font-weight-bold">{{ $org->name }}</div>
                                    <div class="text-muted small">
                                        {{ $org->plan ? $org->plan->name : 'No Plan' }} •
                                        {{ $org->created_at->diffForHumans() }}
                                    </div>
                                </div>
                                <div>
                                    @if($org->is_active)
                                        <span class="badge badge-success">Active</span>
                                    @else
                                        <span class="badge badge-secondary">Inactive</span>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    @else
                        <p class="text-muted">No organizations found.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Subscription Status Breakdown -->
    @if($subscriptionStats->count() > 0)
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Subscription Status Breakdown</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($subscriptionStats as $status => $count)
                            <div class="col-md-3 col-sm-6 mb-3">
                                <div class="text-center">
                                    <div class="h4 font-weight-bold
                                        @if($status === 'active') text-success
                                        @elseif($status === 'trial') text-info
                                        @elseif($status === 'expired') text-danger
                                        @elseif($status === 'canceled') text-warning
                                        @else text-secondary
                                        @endif">
                                        {{ number_format($count) }}
                                    </div>
                                    <div class="text-uppercase text-muted small">{{ ucfirst($status) }}</div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection
