@extends('super_admin.layouts.app')

@section('title', $plan->name . ' - Plan Details')
@section('page-title', 'Plan Details')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ $plan->name }}</h1>
            <p class="text-muted">Plan details and statistics</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('super_admin.plans.edit', $plan) }}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Edit Plan
            </a>
            <a href="{{ route('super_admin.plans.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Plans
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Plan Details -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Plan Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>{{ $plan->name }}</h5>
                            @if($plan->is_featured)
                                <span class="badge bg-warning text-dark mb-2">Featured Plan</span>
                            @endif
                            @if($plan->description)
                                <p class="text-muted">{{ $plan->description }}</p>
                            @endif
                        </div>
                        <div class="col-md-6 text-md-end">
                            <div class="h2 text-primary">${{ number_format($plan->price, 2) }}</div>
                            <small class="text-muted">per month</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Plan Limits -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Plan Limits</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="h3 text-info">{{ $plan->branch_limit == 999 ? '∞' : $plan->branch_limit }}</div>
                            <small class="text-muted">Branches</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h3 text-success">{{ $plan->user_limit == 999 ? '∞' : $plan->user_limit }}</div>
                            <small class="text-muted">Users</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h3 text-purple">{{ $plan->order_limit === null ? '∞' : $plan->order_limit }}</div>
                            <small class="text-muted">Orders/Month</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h3 text-warning">{{ $plan->data_retention_days == 999 ? '∞' : $plan->data_retention_days }}</div>
                            <small class="text-muted">Days Data Retention</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Plan Features -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Features</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-print me-3 {{ $plan->thermal_printing ? 'text-success' : 'text-muted' }}"></i>
                                <div>
                                    <strong>Thermal Printing</strong>
                                    <br><small class="text-muted">Receipt printing capability</small>
                                </div>
                                <div class="ms-auto">
                                    @if($plan->thermal_printing)
                                        <i class="fas fa-check text-success"></i>
                                    @else
                                        <i class="fas fa-times text-muted"></i>
                                    @endif
                                </div>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-chart-bar me-3 {{ $plan->advanced_reporting ? 'text-success' : 'text-muted' }}"></i>
                                <div>
                                    <strong>Advanced Reporting</strong>
                                    <br><small class="text-muted">Detailed analytics and reports</small>
                                </div>
                                <div class="ms-auto">
                                    @if($plan->advanced_reporting)
                                        <i class="fas fa-check text-success"></i>
                                    @else
                                        <i class="fas fa-times text-muted"></i>
                                    @endif
                                </div>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-code me-3 {{ $plan->api_access ? 'text-success' : 'text-muted' }}"></i>
                                <div>
                                    <strong>API Access</strong>
                                    <br><small class="text-muted">REST API for integrations</small>
                                </div>
                                <div class="ms-auto">
                                    @if($plan->api_access)
                                        <i class="fas fa-check text-success"></i>
                                    @else
                                        <i class="fas fa-times text-muted"></i>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-tag me-3 {{ $plan->white_label ? 'text-success' : 'text-muted' }}"></i>
                                <div>
                                    <strong>White Label</strong>
                                    <br><small class="text-muted">Remove branding</small>
                                </div>
                                <div class="ms-auto">
                                    @if($plan->white_label)
                                        <i class="fas fa-check text-success"></i>
                                    @else
                                        <i class="fas fa-times text-muted"></i>
                                    @endif
                                </div>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-palette me-3 {{ $plan->custom_branding ? 'text-success' : 'text-muted' }}"></i>
                                <div>
                                    <strong>Custom Branding</strong>
                                    <br><small class="text-muted">Custom logos and colors</small>
                                </div>
                                <div class="ms-auto">
                                    @if($plan->custom_branding)
                                        <i class="fas fa-check text-success"></i>
                                    @else
                                        <i class="fas fa-times text-muted"></i>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="col-lg-4">
            <!-- Plan Statistics -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="h4 text-primary">{{ number_format($stats['total_organizations']) }}</div>
                        <small class="text-muted">Total Organizations</small>
                    </div>
                    <div class="text-center mb-3">
                        <div class="h4 text-success">{{ number_format($stats['active_organizations']) }}</div>
                        <small class="text-muted">Active Organizations</small>
                    </div>
                    <div class="text-center mb-3">
                        <div class="h4 text-info">{{ number_format($stats['total_subscriptions']) }}</div>
                        <small class="text-muted">Total Subscriptions</small>
                    </div>
                    <div class="text-center mb-3">
                        <div class="h4 text-warning">{{ number_format($stats['active_subscriptions']) }}</div>
                        <small class="text-muted">Active Subscriptions</small>
                    </div>
                </div>
            </div>

            <!-- Revenue Statistics -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Revenue</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="h4 text-success">${{ number_format($stats['monthly_revenue'], 2) }}</div>
                        <small class="text-muted">This Month</small>
                    </div>
                    <div class="text-center">
                        <div class="h4 text-primary">${{ number_format($stats['total_revenue'], 2) }}</div>
                        <small class="text-muted">Total Revenue</small>
                    </div>
                </div>
            </div>

            <!-- Plan Status -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Plan Status</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>Status:</span>
                        @if($plan->is_active)
                            <span class="badge bg-success">Active</span>
                        @else
                            <span class="badge bg-danger">Inactive</span>
                        @endif
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>Created:</span>
                        <span>{{ $plan->created_at->format('M j, Y') }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Updated:</span>
                        <span>{{ $plan->updated_at->format('M j, Y') }}</span>
                    </div>

                    <hr>

                    <div class="d-grid gap-2">
                        @if($plan->is_active)
                            <form method="POST" action="{{ route('super_admin.plans.deactivate', $plan) }}">
                                @csrf
                                <button type="submit" class="btn btn-warning w-100"
                                        onclick="return confirm('Are you sure you want to deactivate this plan?')">
                                    <i class="fas fa-pause me-2"></i>Deactivate Plan
                                </button>
                            </form>
                        @else
                            <form method="POST" action="{{ route('super_admin.plans.activate', $plan) }}">
                                @csrf
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-play me-2"></i>Activate Plan
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
