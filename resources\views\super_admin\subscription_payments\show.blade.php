@extends('super_admin.layouts.app')

@section('title', 'Payment Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Payment Details</h1>
                <div>
                    <a href="{{ route('super_admin.subscription-payments.index') }}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Payments
                    </a>
                    @if($subscriptionPayment->isPending())
                        <form method="POST" action="{{ route('super_admin.subscription-payments.approve', $subscriptionPayment) }}" class="d-inline me-2" id="approve-form-header">
                            @csrf
                            <button type="submit" class="btn btn-success" onclick="return confirmApproval(event, 'approve-form-header')">
                                <i class="fas fa-check"></i> Approve Payment
                            </button>
                        </form>
                        <button type="button" class="btn btn-danger" onclick="showRejectModal()">
                            <i class="fas fa-times"></i> Reject Payment
                        </button>
                    @endif
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error') || $errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') ?? $errors->first() }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Payment Status Alert -->
            @if($subscriptionPayment->isPending())
                <div class="alert alert-warning">
                    <i class="fas fa-clock me-2"></i>
                    <strong>Pending Review:</strong> This payment is waiting for your approval or rejection.
                </div>
            @elseif($subscriptionPayment->isApproved())
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Payment Approved:</strong> This payment has been approved and processed.
                    @if($subscriptionPayment->invoice_number)
                        Invoice #{{ $subscriptionPayment->invoice_number }} has been generated.
                    @endif
                </div>
            @elseif($subscriptionPayment->isRejected())
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle me-2"></i>
                    <strong>Payment Rejected:</strong> This payment was rejected. See notes below for details.
                </div>
            @endif

            <div class="row">
                <div class="col-md-8">
                    <!-- Payment Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Payment Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Payment Reference:</strong></p>
                                    <p class="text-muted">{{ $subscriptionPayment->payment_reference ?: 'N/A' }}</p>

                                    <p><strong>Amount:</strong></p>
                                    <p class="text-muted fs-5 text-primary">${{ number_format($subscriptionPayment->amount, 2) }}</p>

                                    <p><strong>Payment Method:</strong></p>
                                    <p class="text-muted">{{ ucfirst(str_replace('_', ' ', $subscriptionPayment->payment_method)) }}</p>

                                    <p><strong>Payment Date:</strong></p>
                                    <p class="text-muted">{{ $subscriptionPayment->payment_date->format('M d, Y') }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Status:</strong></p>
                                    <p>
                                        <span class="badge bg-{{ $subscriptionPayment->status === 'approved' ? 'success' : ($subscriptionPayment->status === 'pending' ? 'warning' : 'danger') }} fs-6">
                                            {{ ucfirst($subscriptionPayment->status) }}
                                        </span>
                                    </p>

                                    <p><strong>Submitted:</strong></p>
                                    <p class="text-muted">{{ $subscriptionPayment->created_at->format('M d, Y H:i') }}</p>

                                    @if($subscriptionPayment->isApproved())
                                        <p><strong>Approved:</strong></p>
                                        <p class="text-muted">{{ $subscriptionPayment->approved_at->format('M d, Y H:i') }}</p>

                                        <p><strong>Approved By:</strong></p>
                                        <p class="text-muted">{{ $subscriptionPayment->approver_name }}</p>
                                    @endif

                                    @if($subscriptionPayment->invoice_number)
                                        <p><strong>Invoice Number:</strong></p>
                                        <p class="text-muted">{{ $subscriptionPayment->invoice_number }}</p>
                                    @endif
                                </div>
                            </div>

                            @if($subscriptionPayment->notes)
                                <hr>
                                <p><strong>Notes:</strong></p>
                                <div class="bg-light p-3 rounded">
                                    @php
                                        // Try to parse JSON data for plan change details
                                        $notesData = null;
                                        if (str_contains($subscriptionPayment->notes, 'Plan Change Details:')) {
                                            $parts = explode('Plan Change Details:', $subscriptionPayment->notes);
                                            if (count($parts) > 1) {
                                                $jsonPart = trim($parts[1]);
                                                $notesData = json_decode($jsonPart, true);
                                            }
                                        }
                                    @endphp

                                    @if($notesData && is_array($notesData))
                                        {{-- Display formatted plan change details --}}
                                        @if(str_contains($subscriptionPayment->notes, 'Plan Change:'))
                                            <div class="mb-3">
                                                <strong class="text-primary">Plan Change Request</strong>
                                            </div>
                                        @endif

                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6 class="text-info">Change Details</h6>
                                                <ul class="list-unstyled">
                                                    <li><strong>Type:</strong> {{ ucfirst(str_replace('_', ' ', $notesData['type'] ?? 'N/A')) }}</li>
                                                    <li><strong>Change Type:</strong> {{ ucfirst($notesData['change_type'] ?? 'N/A') }}</li>
                                                    @if(isset($notesData['current_plan_id']))
                                                        <li><strong>From Plan ID:</strong> {{ $notesData['current_plan_id'] ?? 'No Plan' }}</li>
                                                    @endif
                                                    @if(isset($notesData['requested_plan_id']))
                                                        <li><strong>To Plan ID:</strong> {{ $notesData['requested_plan_id'] }}</li>
                                                    @endif
                                                </ul>
                                            </div>

                                            @if(isset($notesData['proration']) && is_array($notesData['proration']))
                                                <div class="col-md-6">
                                                    <h6 class="text-success">Pricing Details</h6>
                                                    <ul class="list-unstyled">
                                                        <li><strong>Amount:</strong> ${{ number_format($notesData['proration']['net_amount'] ?? 0, 2) }}</li>
                                                        @if(isset($notesData['proration']['current_plan_credit']) && $notesData['proration']['current_plan_credit'] > 0)
                                                            <li><strong>Credit:</strong> ${{ number_format($notesData['proration']['current_plan_credit'], 2) }}</li>
                                                        @endif
                                                        @if(isset($notesData['proration']['new_plan_charge']))
                                                            <li><strong>New Plan Charge:</strong> ${{ number_format($notesData['proration']['new_plan_charge'], 2) }}</li>
                                                        @endif
                                                        @if(isset($notesData['proration']['remaining_days']))
                                                            <li><strong>Remaining Days:</strong> {{ $notesData['proration']['remaining_days'] }} days</li>
                                                        @endif
                                                    </ul>
                                                </div>
                                            @endif
                                        </div>

                                        @if(isset($notesData['proration']['proration_details']))
                                            <div class="mt-3">
                                                <small class="text-muted">
                                                    <strong>Details:</strong> {{ $notesData['proration']['proration_details'] }}
                                                </small>
                                            </div>
                                        @endif
                                    @else
                                        {{-- Display regular notes --}}
                                        <div style="white-space: pre-wrap;">{{ $subscriptionPayment->notes }}</div>
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Organization & Subscription Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Organization & Subscription Details</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary">Organization</h6>
                                    <p><strong>Name:</strong> {{ $subscriptionPayment->organization->name }}</p>
                                    @if($subscriptionPayment->organization->email)
                                        <p><strong>Email:</strong> {{ $subscriptionPayment->organization->email }}</p>
                                    @endif
                                    @if($subscriptionPayment->organization->phone)
                                        <p><strong>Phone:</strong> {{ $subscriptionPayment->organization->phone }}</p>
                                    @endif
                                    <p><strong>Status:</strong>
                                        <span class="badge bg-{{ $subscriptionPayment->organization->is_active ? 'success' : 'danger' }}">
                                            {{ $subscriptionPayment->organization->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-info">Subscription</h6>
                                    <p><strong>Plan:</strong> {{ $subscriptionPayment->subscription->plan->name }}</p>
                                    <p><strong>Monthly Cost:</strong> ${{ number_format($subscriptionPayment->subscription->plan->price, 2) }}</p>
                                    <p><strong>Period:</strong>
                                        {{ $subscriptionPayment->subscription->start_date->format('M d, Y') }} -
                                        {{ $subscriptionPayment->subscription->end_date->format('M d, Y') }}
                                    </p>
                                    <p><strong>Status:</strong>
                                        <span class="badge bg-{{ $subscriptionPayment->subscription->status === 'active' ? 'success' : 'warning' }}">
                                            {{ ucfirst($subscriptionPayment->subscription->status) }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Subscription Financial Summary -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Financial Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6 class="text-muted">Total Paid</h6>
                                        <h4 class="text-success">${{ number_format($subscriptionPayment->subscription->calculateTotalPaid(), 2) }}</h4>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6 class="text-muted">Outstanding Balance</h6>
                                        <h4 class="text-{{ $subscriptionPayment->subscription->getOutstandingBalance() > 0 ? 'warning' : 'success' }}">
                                            ${{ number_format($subscriptionPayment->subscription->getOutstandingBalance(), 2) }}
                                        </h4>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6 class="text-muted">Pending Payments</h6>
                                        <h4 class="text-info">${{ number_format($subscriptionPayment->subscription->pendingPayments()->sum('amount'), 2) }}</h4>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6 class="text-muted">Total Payments</h6>
                                        <h4 class="text-primary">{{ $subscriptionPayment->subscription->payments()->count() }}</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Quick Actions -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                @if($subscriptionPayment->isPending())
                                    <form method="POST" action="{{ route('super_admin.subscription-payments.approve', $subscriptionPayment) }}" id="approve-form-sidebar">
                                        @csrf
                                        <button type="submit" class="btn btn-success w-100" onclick="return confirmApproval(event, 'approve-form-sidebar')">
                                            <i class="fas fa-check me-2"></i>
                                            Approve Payment
                                        </button>
                                    </form>

                                    <!-- Alternative approval method if CSRF fails -->
                                    <form method="POST" action="{{ route('super_admin.payment.approve.alt', $subscriptionPayment->id) }}" class="mt-2">
                                        @csrf
                                        <button type="submit" class="btn btn-outline-success w-100" onclick="return confirm('Approve this payment using alternative method?')">
                                            <i class="fas fa-check-circle me-2"></i>
                                            Alternative Approve
                                        </button>
                                    </form>

                                    <button type="button" class="btn btn-danger w-100" onclick="showRejectModal()">
                                        <i class="fas fa-times me-2"></i>
                                        Reject Payment
                                    </button>

                                    <a href="{{ route('super_admin.subscription-payments.edit', $subscriptionPayment) }}" class="btn btn-outline-warning">
                                        <i class="fas fa-edit me-2"></i>
                                        Edit Payment
                                    </a>
                                @endif

                                <a href="{{ route('super_admin.subscription-payments.index') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-list me-2"></i>
                                    All Payments
                                </a>

                                <a href="{{ route('super_admin.organizations.show', $subscriptionPayment->organization) }}" class="btn btn-outline-info">
                                    <i class="fas fa-building me-2"></i>
                                    View Organization
                                </a>

                                <a href="{{ route('super_admin.subscriptions.show', $subscriptionPayment->subscription) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-credit-card me-2"></i>
                                    View Subscription
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Payment History -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Recent Payments</h5>
                        </div>
                        <div class="card-body">
                            @php
                                $recentPayments = $subscriptionPayment->subscription->payments()->latest('payment_date')->limit(5)->get();
                            @endphp

                            @if($recentPayments->count() > 0)
                                <div class="list-group list-group-flush">
                                    @foreach($recentPayments as $payment)
                                        <div class="list-group-item px-0 {{ $payment->id === $subscriptionPayment->id ? 'bg-light' : '' }}">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">${{ number_format($payment->amount, 2) }}</h6>
                                                    <small class="text-muted">{{ $payment->payment_date->format('M d, Y') }}</small>
                                                </div>
                                                <span class="badge bg-{{ $payment->status === 'approved' ? 'success' : ($payment->status === 'pending' ? 'warning' : 'danger') }}">
                                                    {{ ucfirst($payment->status) }}
                                                </span>
                                            </div>
                                            @if($payment->payment_reference)
                                                <small class="text-muted">Ref: {{ $payment->payment_reference }}</small>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <p class="text-muted text-center">No payment history available.</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reject Payment Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reject Payment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('super_admin.subscription-payments.reject', $subscriptionPayment) }}">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">Rejection Reason <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="3" required
                                  placeholder="Please provide a clear reason for rejecting this payment..."></textarea>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> Rejecting this payment will notify the organization and they may need to resubmit their payment.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Reject Payment</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showRejectModal() {
    new bootstrap.Modal(document.getElementById('rejectModal')).show();
}

function confirmApproval(event, formId) {
    if (!confirm('Are you sure you want to approve this payment?')) {
        event.preventDefault();
        return false;
    }

    // Refresh CSRF token before submission
    refreshCSRFToken(formId);
    return true;
}

function refreshCSRFToken(formId) {
    // Get the current CSRF token from the meta tag
    const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // Update the form's CSRF token
    const form = document.getElementById(formId);
    const csrfInput = form.querySelector('input[name="_token"]');
    if (csrfInput) {
        csrfInput.value = token;
    }

    // Also update the meta tag with a fresh token if needed
    fetch('/csrf-token', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.token) {
            document.querySelector('meta[name="csrf-token"]').setAttribute('content', data.token);
            if (csrfInput) {
                csrfInput.value = data.token;
            }
        }
    })
    .catch(error => {
        console.log('Could not refresh CSRF token:', error);
        // Continue with existing token
    });
}
</script>
@endsection
