@extends('super_admin.layouts.app')

@section('title', 'Subscriptions')
@section('page-title', 'Subscriptions')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Subscriptions</h1>
            <p class="text-muted">Manage all organization subscriptions</p>
        </div>
        <a href="{{ route('super_admin.subscriptions.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Add Subscription
        </a>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('super_admin.subscriptions.index') }}" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request('search') }}" placeholder="Search organizations or plans...">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="trial" {{ request('status') === 'trial' ? 'selected' : '' }}>Trial</option>
                        <option value="canceled" {{ request('status') === 'canceled' ? 'selected' : '' }}>Canceled</option>
                        <option value="expired" {{ request('status') === 'expired' ? 'selected' : '' }}>Expired</option>
                        <option value="past_due" {{ request('status') === 'past_due' ? 'selected' : '' }}>Past Due</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="plan" class="form-label">Plan</label>
                    <select class="form-select" id="plan" name="plan">
                        <option value="">All Plans</option>
                        @foreach($plans as $plan)
                            <option value="{{ $plan->id }}" {{ request('plan') == $plan->id ? 'selected' : '' }}>
                                {{ $plan->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" 
                           value="{{ request('date_from') }}">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" 
                           value="{{ request('date_to') }}">
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i>
                    </button>
                    <a href="{{ route('super_admin.subscriptions.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Subscriptions Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                Subscriptions ({{ $subscriptions->total() }})
            </h6>
        </div>
        <div class="card-body">
            @if($subscriptions->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Organization</th>
                                <th>Plan</th>
                                <th>Status</th>
                                <th>Period</th>
                                <th>Amount</th>
                                <th>Payment</th>
                                <th>Auto Renew</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($subscriptions as $subscription)
                                <tr>
                                    <td>
                                        <div>
                                            <div class="font-weight-bold">{{ $subscription->organization->name }}</div>
                                            <small class="text-muted">{{ $subscription->organization->email }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $subscription->plan->name }}</span>
                                        <br><small class="text-muted">${{ number_format($subscription->plan->price, 2) }}/month</small>
                                    </td>
                                    <td>
                                        @switch($subscription->status)
                                            @case('active')
                                                <span class="badge bg-success">Active</span>
                                                @break
                                            @case('trial')
                                                <span class="badge bg-info">Trial</span>
                                                @break
                                            @case('canceled')
                                                <span class="badge bg-warning">Canceled</span>
                                                @break
                                            @case('expired')
                                                <span class="badge bg-danger">Expired</span>
                                                @break
                                            @case('past_due')
                                                <span class="badge bg-warning">Past Due</span>
                                                @break
                                            @default
                                                <span class="badge bg-secondary">{{ ucfirst($subscription->status) }}</span>
                                        @endswitch
                                    </td>
                                    <td>
                                        <small>
                                            <strong>Start:</strong> {{ $subscription->start_date->format('M j, Y') }}<br>
                                            <strong>End:</strong> {{ $subscription->end_date->format('M j, Y') }}
                                            @if($subscription->end_date->isPast() && $subscription->status === 'active')
                                                <br><span class="text-danger">Overdue</span>
                                            @endif
                                        </small>
                                    </td>
                                    <td>
                                        <div class="h6 mb-0">${{ number_format($subscription->amount_paid ?? $subscription->plan->price, 2) }}</div>
                                    </td>
                                    <td>
                                        @if($subscription->payment_method)
                                            <span class="badge bg-secondary">{{ ucfirst($subscription->payment_method) }}</span>
                                        @else
                                            <span class="text-muted">Not set</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($subscription->auto_renew)
                                            <i class="fas fa-check text-success" title="Auto-renew enabled"></i>
                                        @else
                                            <i class="fas fa-times text-muted" title="Auto-renew disabled"></i>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('super_admin.subscriptions.show', $subscription) }}" 
                                               class="btn btn-sm btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('super_admin.subscriptions.edit', $subscription) }}" 
                                               class="btn btn-sm btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            
                                            @if($subscription->status === 'active')
                                                <button type="button" class="btn btn-sm btn-outline-warning" 
                                                        title="Cancel" data-bs-toggle="modal" 
                                                        data-bs-target="#cancelModal{{ $subscription->id }}">
                                                    <i class="fas fa-ban"></i>
                                                </button>
                                            @elseif($subscription->status === 'canceled')
                                                <form method="POST" action="{{ route('super_admin.subscriptions.reactivate', $subscription) }}" 
                                                      class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-outline-success" 
                                                            title="Reactivate" onclick="return confirm('Reactivate this subscription?')">
                                                        <i class="fas fa-play"></i>
                                                    </button>
                                                </form>
                                            @endif
                                            
                                            <button type="button" class="btn btn-sm btn-outline-info" 
                                                    title="Extend" data-bs-toggle="modal" 
                                                    data-bs-target="#extendModal{{ $subscription->id }}">
                                                <i class="fas fa-calendar-plus"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Cancel Modal -->
                                <div class="modal fade" id="cancelModal{{ $subscription->id }}" tabindex="-1">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">Cancel Subscription</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <form method="POST" action="{{ route('super_admin.subscriptions.cancel', $subscription) }}">
                                                @csrf
                                                <div class="modal-body">
                                                    <p>Are you sure you want to cancel this subscription for <strong>{{ $subscription->organization->name }}</strong>?</p>
                                                    <div class="mb-3">
                                                        <label for="cancellation_reason{{ $subscription->id }}" class="form-label">Reason for cancellation *</label>
                                                        <textarea class="form-control" id="cancellation_reason{{ $subscription->id }}" 
                                                                  name="cancellation_reason" rows="3" required 
                                                                  placeholder="Please provide a reason for cancellation..."></textarea>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                    <button type="submit" class="btn btn-warning">Cancel Subscription</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- Extend Modal -->
                                <div class="modal fade" id="extendModal{{ $subscription->id }}" tabindex="-1">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">Extend Subscription</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <form method="POST" action="{{ route('super_admin.subscriptions.extend', $subscription) }}">
                                                @csrf
                                                <div class="modal-body">
                                                    <p>Extend subscription for <strong>{{ $subscription->organization->name }}</strong></p>
                                                    <div class="mb-3">
                                                        <label for="extend_months{{ $subscription->id }}" class="form-label">Extend by (months) *</label>
                                                        <select class="form-select" id="extend_months{{ $subscription->id }}" name="extend_months" required>
                                                            <option value="1">1 Month</option>
                                                            <option value="3">3 Months</option>
                                                            <option value="6">6 Months</option>
                                                            <option value="12">12 Months</option>
                                                        </select>
                                                    </div>
                                                    <p class="text-muted">Current end date: {{ $subscription->end_date->format('M j, Y') }}</p>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                    <button type="submit" class="btn btn-info">Extend Subscription</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        Showing {{ $subscriptions->firstItem() }} to {{ $subscriptions->lastItem() }} 
                        of {{ $subscriptions->total() }} results
                    </div>
                    {{ $subscriptions->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No subscriptions found</h5>
                    <p class="text-muted">Get started by creating your first subscription.</p>
                    <a href="{{ route('super_admin.subscriptions.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Subscription
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
