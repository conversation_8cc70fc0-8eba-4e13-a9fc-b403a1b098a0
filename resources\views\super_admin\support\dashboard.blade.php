@extends('super_admin.layouts.app')

@section('title', 'Support Dashboard')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Support Dashboard</h1>
                <div>
                    <button type="button" class="btn btn-outline-primary me-2" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <div class="btn-group">
                        <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-bolt"></i> Quick Actions
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="quickAction('send_overdue_communications')">
                                <i class="fas fa-paper-plane me-2"></i>Send Overdue Communications
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="quickAction('assign_unassigned_tickets')">
                                <i class="fas fa-user-plus me-2"></i>Auto-assign Tickets
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ route('super_admin.support.tickets.create') }}">
                                <i class="fas fa-plus me-2"></i>Create New Ticket
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <!-- Ticket Statistics -->
                <div class="col-xl-3 col-md-6">
                    <div class="card border-left-primary h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Total Tickets
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold">{{ number_format($stats['tickets']['total']) }}</div>
                                    <div class="text-xs text-muted">
                                        {{ $stats['tickets']['open'] }} open • {{ $stats['tickets']['urgent'] }} urgent
                                    </div>
                                </div>
                                <div class="text-primary">
                                    <i class="fas fa-ticket-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Response Time -->
                <div class="col-xl-3 col-md-6">
                    <div class="card border-left-success h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Avg Response Time
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold">{{ $stats['tickets']['avg_response_time'] }}h</div>
                                    <div class="text-xs text-muted">
                                        Resolution: {{ $stats['tickets']['avg_resolution_time'] }}h
                                    </div>
                                </div>
                                <div class="text-success">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Communications -->
                <div class="col-xl-3 col-md-6">
                    <div class="card border-left-info h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Communications
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold">{{ number_format($stats['communications']['total']) }}</div>
                                    <div class="text-xs text-muted">
                                        {{ $stats['communications']['scheduled'] }} scheduled
                                    </div>
                                </div>
                                <div class="text-info">
                                    <i class="fas fa-bullhorn fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Knowledge Base -->
                <div class="col-xl-3 col-md-6">
                    <div class="card border-left-warning h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        KB Articles
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold">{{ number_format($stats['knowledge_base']['published_articles']) }}</div>
                                    <div class="text-xs text-muted">
                                        {{ number_format($stats['knowledge_base']['total_views']) }} views
                                    </div>
                                </div>
                                <div class="text-warning">
                                    <i class="fas fa-book fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alert Cards for Urgent Items -->
            @if($urgentTickets->count() > 0 || $overdueTickets->count() > 0)
            <div class="row mb-4">
                @if($urgentTickets->count() > 0)
                <div class="col-md-6">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Urgent Tickets ({{ $urgentTickets->count() }})
                            </h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                @foreach($urgentTickets as $ticket)
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">
                                                <a href="{{ route('super_admin.support.tickets.show', $ticket) }}" class="text-decoration-none">
                                                    {{ $ticket->title }}
                                                </a>
                                            </h6>
                                            <p class="mb-1 text-muted small">{{ $ticket->organization->name ?? 'N/A' }}</p>
                                        </div>
                                        <div class="text-end">
                                            {!! $ticket->priority_badge !!}
                                            <div class="text-muted small">{{ $ticket->created_at->diffForHumans() }}</div>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
                @endif

                @if($overdueTickets->count() > 0)
                <div class="col-md-6">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-clock me-2"></i>
                                Overdue Tickets ({{ $overdueTickets->count() }})
                            </h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                @foreach($overdueTickets as $ticket)
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">
                                                <a href="{{ route('super_admin.support.tickets.show', $ticket) }}" class="text-decoration-none">
                                                    {{ $ticket->title }}
                                                </a>
                                            </h6>
                                            <p class="mb-1 text-muted small">{{ $ticket->organization->name ?? 'N/A' }}</p>
                                        </div>
                                        <div class="text-end">
                                            {!! $ticket->status_badge !!}
                                            <div class="text-muted small">{{ $ticket->created_at->diffForHumans() }}</div>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            </div>
            @endif

            <!-- Main Content Row -->
            <div class="row">
                <!-- Recent Tickets -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-ticket-alt me-2"></i>
                                Recent Tickets
                            </h5>
                            <a href="{{ route('super_admin.support.tickets.index') }}" class="btn btn-sm btn-outline-primary">
                                View All
                            </a>
                        </div>
                        <div class="card-body p-0">
                            @if($recentTickets->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Ticket</th>
                                                <th>Organization</th>
                                                <th>Priority</th>
                                                <th>Status</th>
                                                <th>Created</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($recentTickets as $ticket)
                                            <tr>
                                                <td>
                                                    <div>
                                                        <strong>{{ $ticket->ticket_number }}</strong>
                                                    </div>
                                                    <div class="text-truncate" style="max-width: 200px;">
                                                        {{ $ticket->title }}
                                                    </div>
                                                </td>
                                                <td>{{ $ticket->organization->name ?? 'N/A' }}</td>
                                                <td>{!! $ticket->priority_badge !!}</td>
                                                <td>{!! $ticket->status_badge !!}</td>
                                                <td>
                                                    <small>{{ $ticket->created_at->format('M d, H:i') }}</small>
                                                </td>
                                                <td>
                                                    <a href="{{ route('super_admin.support.tickets.show', $ticket) }}"
                                                       class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No recent tickets</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Quick Stats -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-chart-pie me-2"></i>
                                Quick Stats
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <div class="h4 mb-0 text-primary">{{ $stats['tickets']['today'] }}</div>
                                        <small class="text-muted">Today</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="h4 mb-0 text-success">{{ $stats['tickets']['this_week'] }}</div>
                                    <small class="text-muted">This Week</small>
                                </div>
                            </div>
                            <hr>
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <div class="h4 mb-0 text-warning">{{ $stats['organizations']['with_open_tickets'] }}</div>
                                        <small class="text-muted">Orgs w/ Tickets</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="h4 mb-0 text-info">{{ $stats['knowledge_base']['avg_helpfulness'] }}%</div>
                                    <small class="text-muted">KB Helpfulness</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Popular KB Articles -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-star me-2"></i>
                                Popular Articles
                            </h6>
                        </div>
                        <div class="card-body p-0">
                            @if($popularArticles->count() > 0)
                                <div class="list-group list-group-flush">
                                    @foreach($popularArticles as $article)
                                    <div class="list-group-item">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">{{ $article->title }}</h6>
                                                <p class="mb-1 text-muted small">{{ $article->category->name ?? 'N/A' }}</p>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-primary">{{ $article->view_count }} views</span>
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-3">
                                    <i class="fas fa-book fa-2x text-muted mb-2"></i>
                                    <p class="text-muted small">No articles yet</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Recent Communications -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-bullhorn me-2"></i>
                                Recent Communications
                            </h6>
                        </div>
                        <div class="card-body p-0">
                            @if($recentCommunications->count() > 0)
                                <div class="list-group list-group-flush">
                                    @foreach($recentCommunications as $comm)
                                    <div class="list-group-item">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">{{ $comm->title }}</h6>
                                                <p class="mb-1 text-muted small">{{ $comm->organization->name ?? 'N/A' }}</p>
                                            </div>
                                            <div class="text-end">
                                                {!! $comm->status_badge !!}
                                                <div class="text-muted small">{{ $comm->created_at->diffForHumans() }}</div>
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-3">
                                    <i class="fas fa-bullhorn fa-2x text-muted mb-2"></i>
                                    <p class="text-muted small">No communications yet</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary { border-left: 0.25rem solid #007bff !important; }
.border-left-success { border-left: 0.25rem solid #28a745 !important; }
.border-left-info { border-left: 0.25rem solid #17a2b8 !important; }
.border-left-warning { border-left: 0.25rem solid #ffc107 !important; }
</style>

<script>
function refreshDashboard() {
    window.location.reload();
}

function quickAction(action) {
    if (confirm('Are you sure you want to perform this action?')) {
        fetch('{{ route("super.support.dashboard.quick-action") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({ action: action })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                window.location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while performing the action.');
        });
    }
}

// Auto-refresh every 5 minutes
setInterval(function() {
    if (!document.hidden) {
        window.location.reload();
    }
}, 300000);
</script>
@endsection
