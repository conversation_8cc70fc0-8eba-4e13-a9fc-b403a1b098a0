@extends('layouts.app')

@section('title', 'Users')

@section('styles')
<link href="/SalesManagementSystem/public/css/sweetalert2.min.css" rel="stylesheet">
<script src="/SalesManagementSystem/public/js/sweetalert2.min.js" defer></script>
<script src="/SalesManagementSystem/public/js/user-management.js?v={{ time() }}" defer></script>
<script src="/SalesManagementSystem/public/js/role-permissions-guide.js?v={{ time() }}" defer></script>
<style>
    .user-stats {
        @apply grid grid-cols-1 md:grid-cols-3 gap-4 mb-6;
    }
    .stat-card {
        @apply bg-white p-4 rounded-lg shadow-sm border border-gray-200;
        transition: all 0.3s ease;
    }
    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    .icon-container {
        transition: all 0.3s ease;
    }
    .stat-card:hover .icon-container {
        transform: scale(1.1);
    }
    .stat-value {
        @apply text-2xl font-bold text-primary;
        background: linear-gradient(120deg, #4F46E5 0%, #7C3AED 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        transition: all 0.3s ease;
    }
    .stat-card:hover .stat-value {
        transform: scale(1.05);
    }
    .stat-label {
        @apply text-sm text-gray-600;
    }
    .filter-section {
        @apply flex flex-wrap items-center gap-4 mb-6;
    }
    .add-user-btn {
        transition: all 0.2s ease;
    }
    .add-user-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    .add-user-btn:active {
        transform: translateY(0);
    }
    .search-input, .filter-select {
        transition: border-color 0.2s ease;
    }
    .search-input:focus, .filter-select:focus {
        border-color: #4F46E5;
        box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
    }
    .filter-select:focus {
        border-color: #4F46E5;
        box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
    }
    .stat-card {
        transition: transform 0.3s ease;
    }
    .stat-card:hover {
        transform: translateY(-5px);
    }
    .icon-container {
        transition: transform 0.3s ease;
    }
    .stat-card:hover .icon-container {
        transform: scale(1.1);
    }
</script>
@endsection

@section('content')
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Debug Info (remove in production) -->
        @if(config('app.debug'))
        <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
            <strong>Debug Info:</strong>
            Active: {{ $activeCount }},
            Inactive: {{ $inactiveCount }},
            Total: {{ $totalUsers }}
        </div>
        @endif

        <!-- Header with Add User Button -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex justify-between items-center">
                <h2 class="text-xl font-semibold text-gray-800">User Management</h2>
                <a href="{{ route('users.create') }}"
                    class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg inline-flex items-center transition duration-150 ease-in-out add-user-btn">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    Add New User
                </a>
            </div>
        </div>

        <!-- Role Permissions Guide -->
        <div class="bg-white shadow-lg rounded-lg mb-8 border border-gray-200">
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-4 rounded-t-lg">
                <h2 class="text-xl font-semibold flex items-center">
                    <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 2.676-1.148 5.16-2.904 7.323-5.114.559-.571 1.077-1.175 1.559-1.808A11.955 11.955 0 0021 9a12.02 12.02 0 00-.382-3.016z" />
                    </svg>
                    Role Permissions Guide
                </h2>
                <p class="text-blue-100 mt-1">Understanding what each role can access and do in your organization</p>
            </div>

            <div class="p-6">
                <!-- Quick Reference Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
                    <!-- Organization Owner Card -->
                    <div class="border-2 border-red-200 rounded-lg p-4 bg-red-50 hover:shadow-lg transition-all duration-200">
                        <div class="flex items-center mb-3">
                            <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                            <h3 class="font-bold text-red-800">Organization Owner</h3>
                        </div>
                        <p class="text-sm text-red-700 mb-2">Complete system control</p>
                        <ul class="text-xs text-red-600 space-y-1">
                            <li>• Full administrative access</li>
                            <li>• User & role management</li>
                            <li>• Organization settings</li>
                            <li>• Financial oversight</li>
                        </ul>
                    </div>

                    <!-- Manager Card -->
                    <div class="border-2 border-orange-200 rounded-lg p-4 bg-orange-50 hover:shadow-lg transition-all duration-200">
                        <div class="flex items-center mb-3">
                            <div class="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                            <h3 class="font-bold text-orange-800">Manager</h3>
                        </div>
                        <p class="text-sm text-orange-700 mb-2">Operations management</p>
                        <ul class="text-xs text-orange-600 space-y-1">
                            <li>• Order management</li>
                            <li>• Financial reports</li>
                            <li>• Expenditure approval</li>
                            <li>• Team oversight</li>
                        </ul>
                    </div>

                    <!-- Account Card -->
                    <div class="border-2 border-green-200 rounded-lg p-4 bg-green-50 hover:shadow-lg transition-all duration-200">
                        <div class="flex items-center mb-3">
                            <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                            <h3 class="font-bold text-green-800">Account</h3>
                        </div>
                        <p class="text-sm text-green-700 mb-2">Financial management</p>
                        <ul class="text-xs text-green-600 space-y-1">
                            <li>• Create expenditures</li>
                            <li>• Financial reports</li>
                            <li>• Account summaries</li>
                            <li>• View orders</li>
                        </ul>
                    </div>

                    <!-- Staff Card -->
                    <div class="border-2 border-blue-200 rounded-lg p-4 bg-blue-50 hover:shadow-lg transition-all duration-200">
                        <div class="flex items-center mb-3">
                            <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                            <h3 class="font-bold text-blue-800">Staff</h3>
                        </div>
                        <p class="text-sm text-blue-700 mb-2">Order processing</p>
                        <ul class="text-xs text-blue-600 space-y-1">
                            <li>• Create & manage orders</li>
                            <li>• Update order status</li>
                            <li>• Delivery updates</li>
                            <li>• View reports</li>
                        </ul>
                    </div>

                    <!-- Production Card -->
                    <div class="border-2 border-purple-200 rounded-lg p-4 bg-purple-50 hover:shadow-lg transition-all duration-200">
                        <div class="flex items-center mb-3">
                            <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                            <h3 class="font-bold text-purple-800">Production</h3>
                        </div>
                        <p class="text-sm text-purple-700 mb-2">Production workflow</p>
                        <ul class="text-xs text-purple-600 space-y-1">
                            <li>• View orders</li>
                            <li>• Update order status</li>
                            <li>• Production tracking</li>
                            <li>• Limited access</li>
                        </ul>
                    </div>

                    <!-- Delivery Card -->
                    <div class="border-2 border-indigo-200 rounded-lg p-4 bg-indigo-50 hover:shadow-lg transition-all duration-200">
                        <div class="flex items-center mb-3">
                            <div class="w-3 h-3 bg-indigo-500 rounded-full mr-2"></div>
                            <h3 class="font-bold text-indigo-800">Delivery</h3>
                        </div>
                        <p class="text-sm text-indigo-700 mb-2">Delivery operations</p>
                        <ul class="text-xs text-indigo-600 space-y-1">
                            <li>• View orders</li>
                            <li>• Update delivery status</li>
                            <li>• Delivery tracking</li>
                            <li>• Limited access</li>
                        </ul>
                    </div>
                </div>

                <!-- Detailed Permissions Table -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2h2a2 2 0 002-2z" />
                        </svg>
                        Detailed Permissions Matrix
                    </h3>

                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Feature/Action</th>
                                    <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Owner</th>
                                    <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Manager</th>
                                    <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Account</th>
                                    <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Staff</th>
                                    <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Production</th>
                                    <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Delivery</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <!-- User Management -->
                                <tr class="bg-red-25">
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900 bg-red-50">User Management</td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 text-sm text-gray-700">Organization Settings</td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                </tr>

                                <!-- Order Management -->
                                <tr class="bg-blue-25">
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900 bg-blue-50">Order Creation</td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 text-sm text-gray-700">View All Orders</td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 text-sm text-gray-700">Update Order Status</td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 text-sm text-gray-700">Update Delivery Status</td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                </tr>

                                <!-- Financial Management -->
                                <tr class="bg-green-25">
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900 bg-green-50">Create Expenditures</td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 text-sm text-gray-700">Approve/Reject Expenditures</td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 text-sm text-gray-700">Financial Reports</td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Role Assignment Recommendations -->
                <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-blue-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                        Role Assignment Recommendations
                    </h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-blue-800 mb-2">👑 Organization Owner</h4>
                            <p class="text-sm text-blue-700 mb-2">Assign to: Business owners, CEOs, or primary administrators</p>
                            <p class="text-xs text-blue-600">⚠️ Limit to 1-2 trusted individuals with full business authority</p>
                        </div>

                        <div>
                            <h4 class="font-semibold text-orange-800 mb-2">👨‍💼 Manager</h4>
                            <p class="text-sm text-orange-700 mb-2">Assign to: Department heads, operations managers</p>
                            <p class="text-xs text-orange-600">✓ Perfect for overseeing daily operations and approving expenditures</p>
                        </div>

                        <div>
                            <h4 class="font-semibold text-green-800 mb-2">💰 Account</h4>
                            <p class="text-sm text-green-700 mb-2">Assign to: Accountants, financial staff</p>
                            <p class="text-xs text-green-600">✓ Ideal for managing expenses and financial tracking</p>
                        </div>

                        <div>
                            <h4 class="font-semibold text-blue-800 mb-2">👥 Staff</h4>
                            <p class="text-sm text-blue-700 mb-2">Assign to: Sales staff, customer service representatives</p>
                            <p class="text-xs text-blue-600">✓ Best for front-line employees who handle customer orders</p>
                        </div>

                        <div>
                            <h4 class="font-semibold text-purple-800 mb-2">🏭 Production</h4>
                            <p class="text-sm text-purple-700 mb-2">Assign to: Production supervisors, manufacturing staff</p>
                            <p class="text-xs text-purple-600">✓ Focused on production workflow and order status updates</p>
                        </div>

                        <div>
                            <h4 class="font-semibold text-indigo-800 mb-2">🚚 Delivery</h4>
                            <p class="text-sm text-indigo-700 mb-2">Assign to: Delivery drivers, logistics coordinators</p>
                            <p class="text-xs text-indigo-600">✓ Specialized for delivery operations and status tracking</p>
                        </div>
                    </div>
                </div>

                <!-- Security Best Practices -->
                <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h4 class="font-semibold text-yellow-800 mb-2 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                        Security Best Practices
                    </h4>
                    <ul class="text-sm text-yellow-700 space-y-1">
                        <li>• <strong>Principle of Least Privilege:</strong> Assign the minimum role necessary for job functions</li>
                        <li>• <strong>Regular Review:</strong> Periodically review and update user roles as responsibilities change</li>
                        <li>• <strong>Owner Access:</strong> Limit Organization Owner role to key decision-makers only</li>
                        <li>• <strong>Role Separation:</strong> Consider separating financial and operational roles for better control</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Tab Navigation -->
        <div class="mb-6">
            <div class="border-b border-gray-200">
                <nav class="flex -mb-px">
                    <button @click="setActiveTab('active')" :class="{ 'border-primary text-primary': activeTab === 'active', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'active' }"
                            class="whitespace-nowrap py-4 px-4 border-b-2 font-medium text-sm">
                        Active Users ({{ $activeCount }})
                    </button>
                    <button @click="setActiveTab('inactive')" :class="{ 'border-yellow-500 text-yellow-500': activeTab === 'inactive', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'inactive' }"
                            class="whitespace-nowrap py-4 px-4 border-b-2 font-medium text-sm">
                        Inactive Users ({{ $inactiveCount }})
                    </button>
                    <button @click="setActiveTab('archived')" :class="{ 'border-gray-500 text-gray-500': activeTab === 'archived', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'archived' }"
                            class="whitespace-nowrap py-4 px-4 border-b-2 font-medium text-sm">
                        Archived Users ({{ $archivedCount }})
                    </button>
                </nav>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="mb-4">
            <div class="flex flex-col sm:flex-row gap-4">
                <div class="flex-1">
                    <input type="text"
                           id="userSearch"
                           placeholder="Search users..."
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 search-input">
                </div>
                <div class="flex flex-col sm:flex-row gap-4 sm:items-center">
                    <select id="roleFilter"
                            class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 filter-select">
                        <option value="">All Roles</option>
                        @foreach($roles as $role)
                            <option value="{{ $role->id }}">{{ $role->name }}</option>
                        @endforeach
                    </select>
                    <select id="statusFilter"
                            class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 filter-select">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                    <button onclick="clearFilters()"
                            class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200">
                        Clear Filters
                    </button>
                </div>
            </div>
        </div>

        <!-- Users Table -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
                @if(session('success'))
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4 alert-dismissible" role="alert">
                        <span class="block sm:inline">{{ session('success') }}</span>
                        <button type="button" class="absolute top-0 right-0 px-4 py-3" onclick="this.parentElement.remove()">
                            <span class="sr-only">Close</span>
                            <svg class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"/>
                            </svg>
                        </button>
                    </div>
                @endif

                <div class="overflow-x-auto">
                    <!-- Active Users Table -->
                    <div x-show="activeTab === 'active'">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Roles</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                                @foreach($activeUsers as $user)
                                <tr data-user-id="{{ $user->id }}" class="bg-white hover:bg-gray-50">
                                        @include('users.partials.user-row', ['user' => $user])
                                </tr>
                            @endforeach
                        </tbody>
                    </table>

                        @if($activeUsers->isEmpty())
                    <div class="text-center py-12 empty-state">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"/>
                        </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">No active users found</h3>
                        <p class="mt-1 text-sm text-gray-500">Get started by creating a new user.</p>
                        <div class="mt-6">
                            <a href="{{ route('users.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                                </svg>
                                Create New User
                            </a>
                        </div>
                    </div>
                @endif
                    </div>

                    <!-- Inactive Users Table -->
                    <div x-show="activeTab === 'inactive'" x-cloak>
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Roles</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($inactiveUsers as $user)
                                    <tr data-user-id="{{ $user->id }}" class="bg-white hover:bg-gray-50">
                                        @include('users.partials.user-row', ['user' => $user])
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>

                        @if($inactiveUsers->isEmpty())
                            <div class="text-center py-12 empty-state">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 14c-3.3 0-6-2.7-6-6s2.7-6 6-6 6 2.7 6 6-2.7 6-6 6zm0-9v3m0 3h.01" />
                        </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">No inactive users found</h3>
                                <p class="mt-1 text-sm text-gray-500">All users are currently active.</p>
                            </div>
                        @endif
                    </div>

                    <!-- Archived Users Table -->
                    <div x-show="activeTab === 'archived'" x-cloak>
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Roles</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($archivedUsers as $user)
                                    <tr data-user-id="{{ $user->id }}" class="bg-white hover:bg-gray-50">
                                        @include('users.partials.user-row', ['user' => $user])
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>

                        @if($archivedUsers->isEmpty())
                            <div class="text-center py-12 empty-state">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">No archived users found</h3>
                                <p class="mt-1 text-sm text-gray-500">You have not archived any users yet.</p>
                        </div>
                        @endif
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="/SalesManagementSystem/public/js/sweetalert2.min.js" defer></script>
<script src="/SalesManagementSystem/public/js/user-management.js?v={{ time() }}" defer></script>
<script src="/SalesManagementSystem/public/js/role-permissions-guide.js?v={{ time() }}" defer></script>
@endpush

@endsection





