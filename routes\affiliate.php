<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Affiliate\AffiliateController;
use App\Http\Controllers\Affiliate\DashboardController;
use App\Http\Controllers\Affiliate\WithdrawalController;
use App\Http\Controllers\Affiliate\ForgotPasswordController;
use App\Http\Controllers\Affiliate\ResetPasswordController;

/*
|--------------------------------------------------------------------------
| Affiliate Routes
|--------------------------------------------------------------------------
|
| These routes handle the affiliate program functionality including
| registration, dashboard, earnings, referrals, and withdrawals.
|
*/

// Public affiliate routes
Route::group(['prefix' => 'affiliate', 'as' => 'affiliate.'], function () {
    // Test route
    Route::get('/test', function () {
        return view('affiliate.test');
    })->name('test');

    // Guest routes (for affiliate guard) - temporarily without middleware for testing
    Route::get('/register', [AffiliateController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [AffiliateController::class, 'register']);
    Route::get('/login', [AffiliateController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AffiliateController::class, 'login']);
    Route::get('/program', [AffiliateController::class, 'showProgram'])->name('program');

    // Password reset routes
    Route::get('/forgot-password', [ForgotPasswordController::class, 'create'])->name('password.request');
    Route::post('/forgot-password', [ForgotPasswordController::class, 'store'])->name('password.email');
    Route::get('/reset-password/{token}', [ResetPasswordController::class, 'create'])->name('password.reset');
    Route::post('/reset-password', [ResetPasswordController::class, 'store'])->name('password.store');

    // Pending approval route (requires auth but not active status)
    Route::middleware('auth:affiliate')->group(function () {
        Route::get('/pending', [AffiliateController::class, 'showPending'])->name('pending');
    });

    // Pending approval page
    Route::get('/pending', [AffiliateController::class, 'showPending'])->name('pending');

    // Authenticated affiliate routes
    Route::middleware(['auth:affiliate', \App\Http\Middleware\AffiliateMiddleware::class])->group(function () {
        Route::post('/logout', [AffiliateController::class, 'logout'])->name('logout');

        // Dashboard
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

        // Referral management
        Route::get('/referrals', [DashboardController::class, 'referrals'])->name('referrals');
        Route::get('/referral-tools', [DashboardController::class, 'referralTools'])->name('referral-tools');

        // Earnings
        Route::get('/earnings', [DashboardController::class, 'earnings'])->name('earnings');

        // Withdrawals
        Route::get('/withdrawals', [WithdrawalController::class, 'index'])->name('withdrawals');
        Route::get('/withdrawals/create', [WithdrawalController::class, 'create'])->name('withdrawals.create');
        Route::post('/withdrawals', [WithdrawalController::class, 'store'])->name('withdrawals.store');
        Route::get('/withdrawals/{withdrawal}', [WithdrawalController::class, 'show'])->name('withdrawals.show');
        Route::delete('/withdrawals/{withdrawal}', [WithdrawalController::class, 'cancel'])->name('withdrawals.cancel');

        // Profile management
        Route::get('/profile', [AffiliateController::class, 'profile'])->name('profile');
        Route::put('/profile', [AffiliateController::class, 'updateProfile'])->name('profile.update');

        // Click analytics API
        Route::get('/api/click-analytics', function() {
            $affiliate = request()->affiliate ?? \App\Models\Affiliate::where('user_id', auth()->guard('affiliate')->id())->first();
            if (!$affiliate) {
                return response()->json(['error' => 'Affiliate not found'], 404);
            }

            $controller = new \App\Http\Controllers\AffiliateClickController();
            return $controller->getClickAnalytics(request(), $affiliate->id);
        })->name('api.click-analytics');
    });
});
